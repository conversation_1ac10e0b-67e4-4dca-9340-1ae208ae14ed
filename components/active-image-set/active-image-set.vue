<template>
    <view>
        <view class="form-item">
            <view class="top color-title">
                <text>活动主图</text>
                <text class="font12 color-sub pl5">(设置活动缩略图及活动详情顶部图片)</text>
                <view class="font12 color-sub">图片尺寸建议: 750*430</view>
            </view>
            <view style="padding-top: 5px;">
                <view class="image-view" v-if="Logo">

                    <image class="image-item" :src="Logo" mode="aspectFill" @click="previewImage([Logo])"/>
                    <view class="del-image-item" @click.stop="Logo = ''">
                        <uni-icons type="closeempty" color="#e20f04"/>
                    </view>
                </view>

                <view v-else class="add-image text-center" @click="changeImage('logo')">
                    <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                </view>
            </view>
        </view>

        <view class="form-item">
            <view class="top color-title">
                <text>活动页面开屏大图</text>
                <view class="font12 color-sub">图片尺寸建议: 780*1387</view>
            </view>
            <view style="padding-top: 5px;">
                <view class="image-view" v-if="screen_pic">

                    <image class="image-item" :src="screen_pic" mode="aspectFill"
                           @click="previewImage([screen_pic])"/>
                    <view class="del-image-item" @click.stop="screen_pic = ''">
                        <uni-icons type="closeempty" color="#e20f04"/>
                    </view>
                </view>
                <view v-else class="add-image text-center" @click="changeImage('screen_pic')">
                    <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                </view>
            </view>
        </view>

        <view class="form-item">
            <view class="top">
                <text class="color-title">排行榜顶部轮播图</text>
                <text class="color-sub font12 pl5">
                    {{ top_rank_banner.length }}/{{ top_rank_banner_max_count }}
                </text>
                <view class="font12 color-sub">图片尺寸建议: 640*272</view>
            </view>
            <view style="padding-top: 5px;">
                <view class="flex-row flex-wrap">
                    <view class="top-rank-banner-item"
                          v-for="(item, index) in top_rank_banner" :key="index">
                        <image :src="item" mode="aspectFill"
                               @click="previewImage(top_rank_banner, item)"/>
                        <view class="del-image-item"
                              @click.stop="top_rank_banner.splice(index, 1)">
                            <uni-icons type="closeempty" color="#e20f04"/>
                        </view>
                    </view>
                    <view v-if="top_rank_banner.length < top_rank_banner_max_count"
                          class="add-image text-center" @click="changeImage('top_rank_banner')">
                        <uni-icons type="plusempty" size="48" color="#eeeeee"/>
                    </view>
                </view>
            </view>
        </view>
    </view>
</template>

<script>
export default {
    name: "active-image-set",
    props: ['activeId', 'rankSet', 'logo', 'screenPic', 'topRankBanner'],
    data() {
        return {
            Logo: '',
            screen_pic: '',
            top_rank_banner: [],
            top_rank_banner_max_count: 6
        }
    },

    watch: {
        logo: {
            handler(val) {
                this.Logo = val
            },
            immediate: true
        },
        screenPic: {
            handler(val) {
                this.screen_pic = val
            },
            immediate: true
        },
        topRankBanner: {
            handler(val) {
                this.top_rank_banner = val
            },
            deep: true,
            immediate: true
        },

        Logo(val) {
            this.$emit('update:logo', val)
        },
        screen_pic(val) {
            this.$emit('update:screenPic', val)
        },
        top_rank_banner: {
            handler(val) {
                this.$emit('update:topRankBanner', val)
            },
            deep: true
        },
    },

    methods: {
        previewImage(urls, current = urls[0]) {
            this.$uni.previewImage({urls, current})
        },

        changeImage(key) {
            if (!this.rankSet?.closed_AD) {
                if (key === 'screen_pic') return this.$uni.showModal('无法设置开屏大图，请联系客服设置')
                if (key === 'top_rank_banner') return this.$uni.showModal('无法设置排行榜轮播图，请联系客服设置')
            }

            let url = '/pages/other/image_upload_or_select'
            if (this.activeId) url += `?active_id=${this.activeId}`

            this.$uni.navigateTo(url, {
                events: {
                    newImg: src => {
                        if (key === 'top_rank_banner') {
                            this.top_rank_banner.push(src)
                        } else {
                            this[key] = src
                        }
                    }
                }
            })
        }
    }
}
</script>

<style lang="scss">
.add-image {
    border: 1px solid #eee;
    box-sizing: border-box;
    width: calc((100vw - 50px) / 3);
    aspect-ratio: 1;
    border-radius: 5px;
    margin: 5px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.image-item {
    width: calc(100vw - 20px);
    height: 50vw;
    border-radius: 5px;
}

.image-view {
    position: relative;
}

.del-image-item {
    position: absolute;
    right: 10px;
    top: 8px;
    width: 30px;
    height: 30px;
    line-height: 30px;
    text-align: center;
    background-color: rgba(0, 0, 0, .5);
    border-radius: 50%;
}

.top-rank-banner-item {
    padding: 5px;
    position: relative;
}

.top-rank-banner-item image {
    width: calc((100vw - 50px) / 3);
    height: calc((100vw - 50px) / 3);
    border-radius: 5px;
}

.top-rank-banner-item .del-image-item {
    right: 8px;
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .image-item {
        width: 500px;
        height: 200px;
    }

    .top-rank-banner-item image, .add-image {
        width: 150px;
    }
}

/* #endif */
</style>