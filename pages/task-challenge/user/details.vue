<template>
    <view class="page">

        <screen-picture
            v-if="screen_pic_show"
            :hide="screen_pic_hide"
            :image-src="screen_pic"
            :time="screen_pic_count_down"
            :show-button="true"
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="!init_loading && detail && detail.active_id">
            <xwy-ad v-if="(!rank_set.closed_AD) && !screen_pic_show" :ad_type="3"/>

            <view class="bg-music" v-if="backgroundMusic">
                <bg-music :src="backgroundMusic"/>
            </view>
            
            <view class="current-level-image-container" v-if="current_level_image">
                <template>

                    <image v-if="levelImageFixed" class="current-level-image" style="height: 100vh"
                           :src="current_level_image" mode="aspectFill"/>

                    <image v-else class="current-level-image" :src="current_level_image" mode="widthFix"
                       @load="currentLevelImageLoad" @click="currentLevelImageClick"/>
                </template>

                <template v-if="!levelImageFixed && image_diy_task_set.length">
                    <view class="image-diy-task" v-for="(item, index) in image_diy_task_set" :key="index"
                          :style="{width: item.width, height: item.height, top: item.y, left: item.x}"
                          @click="toImageDiyTask(item.types)"
                    ></view>
                </template>
            </view>

            <template v-if="is_joining">
                <view v-if="!justOneTask" class="top-msg top-msg-left" @click="GO">
                    <view class="top-msg-icon">
                        <view class="top-msg-inner-ring">
                            <text class="iconfont icon-map"></text>
                        </view>
                    </view>
                    <view class="top-msg-num">
                        <template v-if="justOneLevelNotLevel">
                            <template v-if="isNaN(activeDays)">{{ activeDays }}</template>
                            <template v-else>第{{ activeDays }}天</template>
                        </template>
                        <template v-else>{{ levelNames[current_level_index] }}</template>
                    </view>
                </view>

                <view class="top-msg top-msg-right" @click="lookIntegralRecord">
                    <view class="top-msg-icon">
                        <view class="top-msg-inner-ring">
                            <text class="iconfont icon-integral"></text>
                        </view>
                    </view>
                    <view class="top-msg-num" :class="{'need-show-text' : !!rank_set.medalSetting}">
                        <text v-if="rank_set.medalSetting" class="font12 pr5">我的{{ integral_unit }}:</text>
                        <template>
                            <template v-if="rank_set['rushing_round_index_current_day_integral']">
                                {{ todayIntegral }}
                            </template>
                            <template v-else>{{ user_details['integral_all'] }}</template>
                        </template>
                    </view>
                </view>

                <view v-if="rank_set.medalSetting" class="top-msg top-msg-right top-msg-medal"
                      @click="lookMedal3">
                    <view class="top-msg-icon">
                        <view class="top-msg-inner-ring">
                            <text class="iconfont icon-medal"></text>
                        </view>
                    </view>
                    <view class="top-msg-num need-show-text">
                        <text class="font12 pr5">我的勋章:</text>
                        <text>{{ user_details.medal_num || 0 }}</text>
                    </view>
                </view>
            </template>


            
            <view v-show="runButtonShow" class="action-button action-button-fixed"
                  :style="{backgroundColor: start_challenge_button_set.bg_color, color: start_challenge_button_set.text_color}"
                  @click="joinOrStart">
<!--                {{ is_joining ? start_challenge_button_set.text : '参与活动'}}-->
                {{ start_challenge_button_set.text }}
            </view>


            <icon-list-show
                :active-set="detail.conf.active"
                :icon-list="right_icons_set"
                :is-joining="is_joining"
                :notice-show="!!active_details_notice_list.length"
                @iconItemClick="iconItemClick"
            />

            <body-data-submit ref="bodyDataSubmit" :active-id="id" :show-ad="!rank_set.closed_AD"
                              @success="bodyDataSubmitSuccess"/>
        </view>
        

        <must-submit-popup 
            ref="mustSubmitPopup"
            :active-id="id"
            :must-submit="must_submit"
            :update="update_attend_details"
            :show-team="!!rank_set.team_group_open"
            :team-id="user_details.team_id || 0"
            :team-name="user_details.team_details && user_details.team_details.name || ''"
            :team-required="teamRequired"
            :show-ad="!!rank_set.closed_AD"
            :batch-import="batchImportOpen"
            :batch-import-label="batchImportLabel"
            :user-sex="user_details.sex || 0"
            :sex-require="detail.conf.active.sex_required || 0"
            :sex-label="sexLabel"
            @close="mustSubmitPopupClose"
            @mustSubmitUpdate="mustSubmitUpdate"
            @trialNoJoinJustGoTask="trialNoJoinJustGoTask"
        />

        <share-popup v-if="detail.id" ref="sharePopup" :short-id="detail.id"
                     :qrcode-logo="detail.conf.active.qrcode_logo"/>
        

        <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info detail-popup">

                <view class="uni-popup-info-title">活动说明</view>

                <view class="popup-container">
                    <scroll-view scroll-y="true" class="detail-popup-detail">

                        <view v-if="!detail.conf.active.activity_rules_hide" style="padding-bottom: 30px;">
                            <!--<view class="text-center color-sub pb5">- 活动规则 -</view>-->
                            <view class="color-content font16">
                                活动参与方式：
                                {{ ['', '自由报名参与活动', '需要输入密码才能报名', '报名需要审核通过才能参与活动', '使用管理员导入的名单报名'][detail.conf.active.enter_types] }}
                            </view>
                        </view>

                        <view v-if="detail.content || news_detail">
                            <!--<view class="text-center color-sub pb5">- 活动说明 -</view>-->
                            <view class="color-content font16">
                                <template v-if="detail.content && !news_detail">
                                    <rich-text :nodes="detail.content" space="nbsp"></rich-text>
                                </template>
                                <template v-if="news_detail">
                                    <template v-if="news_detail.content">
                                        <u-parse :content="news_detail.content"/>
                                    </template>
                                </template>
                            </view>
                        </view>
                    </scroll-view>

                    <view v-if="!loading && !rank_set.closed_AD" class="flex-center">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                </view>
            </view>
            <view class="flex-all-center" @click="uniPopupClose('activity_detail')">
                <uni-icons type="close" size="28" color="#ffffff"/>
            </view>
        </uni-popup>

        <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info">
                <view class="uni-popup-info-title">活动报名信息</view>
                <view class="popup-container">
                    <view class="text-center p10">

                        <image
                            class="headimg"
                            :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                            mode="aspectFill"
                        />
                        <view>
                            <text class="color-primary" @click="updateHeadimg">更改头像</text>
                        </view>
                    </view>

                    <view
                        class="user-info-item color-content"
                        v-for="(item, index) in must_submit"
                        :key="index"
                        @click="updateAttendDetailShow"
                    >
                        <text>{{item.title}}:</text>
                        <text class="plr5">
                            <template>
                                <template v-if="item.value">{{ item.value }}</template>
                                <template v-else>
                                    <template v-if="item.types === 1">未填写</template>
                                    <template v-if="item.types === 2">未选择</template>
                                </template>
                            </template>
                        </text>
                        <text v-if="is_joining" class="iconfont icon-edit color-sub"></text>
                    </view>

                    <view v-if="detail.conf.active.sex_required !== -1" class="user-info-item color-content"
                          @click="updateAttendDetailShow">
                        <text>性别:</text>
                        <text class="plr5">{{ sexTitle || '保密' }}</text>
                        <text v-if="is_joining" class="iconfont icon-edit color-sub"></text>
                    </view>

                    <view v-if="rank_set.team_group_open" class="user-info-item color-content"
                          @click="updateAttendDetailShow">
                        <text>队伍:</text>
                        <text class="plr5">
                            {{ user_details.team_details && user_details.team_details.name || user_details.team_id || '未加入队伍' }}
                        </text>
                        <text v-if="is_joining && !user_details.team_id" class="iconfont icon-edit color-sub"></text>
                    </view>

                    <view v-if="!justOneLevelNotLevel" class="user-info-item color-content">
                        <text>当前关卡:</text>
                        <text class="plr5">{{ levelNames[current_level_index] }}</text>
                    </view>

                    <view class="user-info-item color-content" @click="lookIntegralRecord">
                        <text>累计{{ integral_unit }}:</text>
                        <text class="pl5">{{ user_details['integral_all'] }}</text>
                        <text class="iconfont icon-more color-content"></text>
                    </view>

                    <view v-if="rank_set.gift_goods && !detail.conf.active.integral_left_hide"
                          class="user-info-item color-content" @click="toShop">
                        <text>剩余{{ integral_unit }}:</text>
                        <text class="pl5">{{ user_details['integral_left'] }}</text>
                        <text class="iconfont icon-more color-content"></text>
                    </view>

                    <template v-if="bmi_value">
                        <view class="user-info-item color-content" @click="uniPopupOpen('bmiAnalysisPopup')">
                            <text>BMI:</text>
                            <text class="plr5">{{ bmi_value.value }}</text>
                            <text class="font14">({{ bmi_value.category }})</text>
                            <text class="iconfont color-sub icon-question-mark-up font18"></text>
                        </view>
                        <bmi-analysis-popup ref="bmiAnalysisPopup" :bmi-value="bmi_value.value"
                                            :bmi-text="bmi_value.category"/>
                    </template>

                    <view v-if="rank_set.medalSetting" class="user-info-item color-content"
                          @click="lookMedal3">
                        <text>我的勋章:</text>
                        <text class="pl5">{{ user_details.medal_num || 0 }}枚</text>
                        <text class="iconfont icon-more color-content"></text>
                    </view>

                    <view v-if="who === 288 && evn_version !== 'release'"
                          class="user-info-item color-content" @click="toActiveReport">
                        <text>活动报告:</text>
                        <text class="pl5">查看报告</text>
                        <text class="iconfont icon-more color-content"></text>
                    </view>


                    <template v-if="!rank_set.closed_AD">
                        <view class="flex-center">
                            <xwy-ad :ad_type="66"></xwy-ad>
                        </view>
                        <xwy-ad :ad_type="3"></xwy-ad>
                    </template>
                </view>
            </view>

            <view class="flex-all-center" @click="uniPopupClose('my_info')">
                <uni-icons type="close" size="28" color="#ffffff"/>
            </view>
        </uni-popup>




        <uni-popup ref="input_password" :is-mask-click="false" @maskClick="copy(id, true)">
            <view class="password-popup">
                <view class="password-popup-title">活动密码</view>
                <view class="popup-container">
                    <view class="password-popup-input">
                        <uni-easyinput v-model="password" placeholder="请输入活动密码"/>
                    </view>
                    <view class="password-popup-buttons">
                        <view class="password-popup-cancel-button" @click="passwordInputClose">取消</view>
                        <view class="password-popup-confirm-button" @click="passwordInputConfirm">确定</view>
                    </view>
                </view>
            </view>
        </uni-popup>

        <expiration-reminder ref="expirationReminder"/>


        <view v-if="init_loading" class="text-center" style="padding-top: 30vh;">
            <load-ani />
            <view class="font14 color-sub">活动加载中</view>
        </view>


        <uni-popup ref="task_list" type="bottom" :safe-area="false">
            <view class="flex-kai">
                <view></view>
                <view class="p10">
                    <uni-icons type="close" color="#ffffff" size="28" @click="uniPopupClose('task_list')"/>
                </view>
            </view>
            <view class="task-list-popup" :style="{backgroundColor: taskPopupStyle.border_color}">
                <view class="popup-container" :style="{backgroundColor: taskPopupStyle.bg_color}">
                    <view class="task-list-popup-title" @click="copy(id, true)">
                        <template v-if="justOneLevelNotLevel">
                            <template v-if="isNaN(activeDays)">{{ activeDays }}</template>
                            <template v-else>
                                <text>第</text>
                                <text class="font24 plr5">{{ activeDays }}</text>
                                <text>天</text>
                            </template>
                        </template>
                        <template v-else>{{ levelNames[current_level_index] }}</template>
                    </view>

                    <view v-if="task_list_loading" class="text-center" style="padding: 100px 0;">
                        <load-ani/>
                        <view class="font12" :style="{color: taskPopupStyle.text_color}">任务加载中</view>
                    </view>

                    <view v-if="level_task_description" class="task-tips font12 text-center pt10"
                          :style="{color: taskPopupStyle.text_color}">
                        <text>{{ level_task_description }}</text>
                        <text v-if="is_joining && detail.conf.active.job_set.types === 2" class="pl5">
                            (已获得{{ user_details['integral_all'] }}{{ integral_unit }})
                        </text>
                    </view>
                    <view class="task-list">
                        <view v-for="(item, index) in task_list" :key="index">
                            <!--鲜繁客户定制活动，需要在这里跳转到运动圈列表
                            因为他这个客户开启了每日刷新任务rush_round_refresh_daily，所以通过这个来判断-->
                            <task-list-item v-if="!item.hidden" :item="item"
                                            :style="taskPopupStyle.task_item"
                                            :active-id="id"
                                            :sport-moment-name="sportMomentName"
                                            :hide-sign-list-enter="!!detail.conf.active.week_sport_task_hide_sign_list_enter"
                                            @toCompleteTask="toCompleteTask(item)"
                                            @lookWeekTaskNews="lookWeekTaskNews"
                                            @toSportMomentList="toSportMomentList"/>
                        </view>

                        <task-list-item v-if="taskListShowShopSet" :show-sport-moment="false"
                                        :item="taskListShowShopSet" :style="taskPopupStyle.task_item"
                                        @toCompleteTask="toShop"/>

                        <task-list-item v-if="taskListShowLotterySet" :show-sport-moment="false"
                                        :item="taskListShowLotterySet" :style="taskPopupStyle.task_item"
                                        @toCompleteTask="toLottery"/>

                        <task-list-item v-if="taskListShowSignSet" :show-sport-moment="false"
                                        :item="taskListShowSignSet" :style="taskPopupStyle.task_item"
                                        @clickTitle="toSignSquare" @toCompleteTask="toSign"/>
                    </view>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="next_level">
            <view class="uni-popup--" style="padding-bottom: 30px;">
                <view class="uni-popup--title">解锁关卡</view>
                <view class="popup-container text-center">
                    <view class="color-warning">恭喜你已解锁</view>
                    <view class="color-warning font24" style="padding: 30px 0;">
                        『 {{ levelNames[current_level_index] }} 』
                    </view>
                    <view class="action-button" @click="goNext">去闯关</view>
                </view>
            </view>
        </uni-popup>

        <uni-popup ref="lookCertificatePopup">
            <view class="uni-popup--" style="padding-bottom: 30px;">
                <view class="uni-popup--title">解锁证书</view>
                <view class="popup-container text-center">
                    <view style="padding-top: 20px; padding-bottom: 10px;">
                        <text class="iconfont icon-certificate color-light-primary"
                          style="font-size: 100px;"></text>
                    </view>
                    <view class="color-warning pb10">恭喜你已获得证书</view>
                    <view class="action-button" @click="lookCertificate">查看</view>

                    <view class="flex-all-center">
                        <view class="p10 color-sub font14" @click="uniPopupClose('lookCertificatePopup')">
                            稍后查看
                        </view>
                    </view>
                </view>
            </view>
        </uni-popup>


        <notice-popup ref="notice_popup" :is-join="is_joining" :show-user-center="!!rank_set.shield_other"
                      :notice-list="active_details_notice_list"/>
    </view>
    
</template>

<script>

const app = getApp()
const {who, evn_version} = app.globalData
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import my_storage from '@/utils/storage.js'
import mustSubmitPopup from '../components/must-submit-popup.vue'
import config from '../config'
import iconListShow from '../components/icon-list-show.vue'
import noticePopup from "../components/notice-popup.vue"
import sharePopup from "../components/share-popup.vue"
import taskListItem from "../components/task-list-item.vue"
import bgMusic from "../components/bg-music.vue"
import bodyDataSubmit from '../components/body-data-submit.vue'
import bmiAnalysisPopup from '../components/bmi-analysis-popup.vue'

let interval

export default {
    components: {
        mustSubmitPopup,
        iconListShow,
        noticePopup, sharePopup,
        taskListItem,
        bgMusic,
        bodyDataSubmit,
        bmiAnalysisPopup
    },
    data() {
        return {
            who,
            evn_version,
            loading: true,
            init_loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            user_details: {},
            error: '',
            update_attend_details: false,
            headimg: '',
            checked: 0,
            is_joining: false,
            password: '',
            must_submit: [],
            news_detail: null,
            current_level_id: null,
            current_level_image: '',
            // 关卡背景图是否不能滑动
            levelImageFixed: false,
            task_list: [],
            task_list_loading: false,
            task_list_level_number: 0,
            current_level_index: 0,
            icon_list_show_more: false,
            level_task_description: '',
            integral_unit: '积分',
            
            right_icons_set: [],
            icon_list_show_count: 4, // 默认多少个显示出来

            rank_set: {},

            active_details_notice_list: [],
            start_challenge_button_set: config.getDefaultStartButtonSet(),

            level_count: 0,
            levelNames: [],

            image_diy_task_set: [],

            todayIntegral: 0,

            bmi_value: null
        }
    },

    watch: {
        current_level_id(newVal, oldVal) {
            if (newVal !== oldVal) this.setImageDiyTaskSet(newVal)
        }
    },
    
    computed: {
        // 开启了每日刷新任务的，并且只添加了1个关卡的，等于无需关卡，每日刷新这个关卡的任务
        justOneLevelNotLevel() {
            return this.rank_set?.rush_round_refresh_daily === 1 && this.level_count === 1
        },

        justOneTask() {
            return this.justOneLevelNotLevel && this.detail?.conf?.active?.job_set?.job_list.length === 1
        },
        
        activeDays() {
            const { begin_time: start_time, end_time } = this.detail || {}
            if (!start_time || !end_time) return 0

            const now = new Date().getTime()
            if ((start_time * 1000) > now) return '活动未开始'

            // 这里取的是活动开始、结束时间的日期，不需要时分秒，不然天数计算会有问题。
            // 如: 使用活动设置的时分秒，活动开始时间是昨天的8点，那么今天8点以前活动天数会显示为第1天，过了8点才显示为第二天。
            // 不使用活动设置的时分秒，从0点0分0秒开始计算，是第几天就第几天
            const start_date = this._utils.unitTimeToDate(start_time * 1000)
            const end_date = this._utils.unitTimeToDate(end_time * 1000)

            const today = this._utils.getDay(0, true)
            const all_days = this._utils.dateDiff(end_date, start_date)
            const activeDays = this._utils.dateDiff(today, start_date)
            return Math.min(activeDays, all_days)
        },

        batchImportOpen() {
            if (!this.rank_set?.batch_import) return false
            return this.detail?.conf?.active?.enter_types === 4
        },

        batchImportLabel() {
            return this.detail?.conf?.active?.batch_import_label || {
                username: '请输入账号',
                password: '请输入密码'
            }
        },

        taskListShowShopSet() {
            const {show, set} = this.detail?.conf?.active?.task_list_show_shop || {}
            if (this.rank_set?.gift_goods && show && set) {
                return {
                    complete_button_text: set.button_text || '',
                    describe: set.text || '',
                    logo: set.logo || '',
                    title: set.title || ''
                }
            }
            return null
        },

        taskListShowLotterySet() {
            const {show, set} = this.detail?.conf?.active?.task_list_show_lottery || {}
            if (this.rank_set?.['lottery_open'] && show && set) {
                return {
                    complete_button_text: set.button_text || '',
                    describe: set.text || '',
                    logo: set.logo || '',
                    title: set.title || ''
                }
            }
            return null
        },

        taskListShowSignSet() {
            const {show, set} = this.detail?.conf?.active?.task_list_show_sign || {}
            if (this.rank_set?.medalSetting && show && set) {
                return {
                    complete_button_text: set.button_text || '',
                    describe: set.text || '',
                    logo: set.logo || '',
                    title: set.title || ''
                }
            }
            return null
        },

        taskPopupStyle() {
            // 没有开通数据导出功能不能自定义设置任务列表样式
            if (!this.rank_set?.['export_top_rank_excel']) return config.taskPopupDefaultStyle()
            return this.detail?.conf?.active?.['task_popup_style'] || config.taskPopupDefaultStyle()
        },

        sportMomentName() {
            return this.detail?.conf?.active?.sport_moment_name || '运动圈'
        },

        // 将任务diy设置在关卡图片上，不用显示去闯关按钮
        runButtonShow() {
            return !this.image_diy_task_set.length
        },

        backgroundMusic() {
            if (!this.rank_set?.open_mp3) return ''
            return this.detail?.conf?.active?.audio_src || ''
        },

        sexLabel() {
            const {man = '男', woman = '女'} = this.detail?.conf?.active?.sex_label || {}
            return [
                {value: 1, title: man},
                {value: 2, title: woman}
            ]
        },

        sexTitle() {
            const sex = this.user_details?.sex
            if (!sex) return ''
            return this.sexLabel.find(item => item.value === sex)?.title || ''
        },

        teamRequired() {
            return !!this.detail?.conf?.active?.team_required
        },
    },

    onLoad(e) {
        console.log('活动详情页面路径参数', e)

        let is_h5 = false
        // #ifdef H5
        is_h5 = true
        // #endif
        if (is_h5) return xwy_api.alert('请在小程序内打开', {success: () => uni.navigateBack()})


        login.uniLogin(err => {
            if (err && err.errMsg) {
                this.init_loading = false
                this.loading = false
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getId(e)
        })
    },

    onShow() {
        this.stepTaskAnswerScoreCheck()
    },


    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        let url = '/pages/task-challenge/user/details?id=' + this.id;
        if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`
        
        return {
            title: this.detail?.conf?.active?.share_title || this.detail?.name || '',
            path: url,
            imageUrl: this.detail?.conf?.active?.share_image || this.detail?.logo || ''
        }
    },

    methods: {
        async getId (e) {
            let id = e.id
            if (!id && e.scene) id = await this.analysisSceneId(e.scene)

            if (!id) {
                this.loading = false
                this.init_loading = false
                this.error = '请指定活动id'
                return
            }

            this.id = id
            this.userid = app.globalData['userid']

            await this.init()
        },

        async init() {
            if (!await this.getDetail()) {
                this.loading = false
                this.init_loading = false
                return
            }

            await this.reLoadData(true)

            this.loading = false
            this.init_loading = false
        },

        async reLoadData(init = false) {
            await this.getUserStatus()
            await this.getLevelList()
            await this.setCurrentLevel()
            if (!init) await this.certificateUnlockCheck()
        },


        async analysisSceneId(scene) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = utils.getUrlParams('id', sceneStr)
            console.log('id===', id)
            if (!id) return false

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data: {id}
            })

            return res?.data?.['long_active_id'] || false
        },



        screenPicShow(src) {
            // #ifndef H5
            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true
            interval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) {
                    this.skipScreen()
                }
            }, 1000)
            // #endif
        },
        skipScreen() {
            clearInterval(interval)
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                this.passwordDialogShow()
            }, 500)
        },




        async getDetail(just_update = false) {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details',
                data: {active_id: this.id},
            })


            if (!res?.data?.['active_details']) {
                this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                return false
            }

            if (res.data.active_more_data?.['active_conf_set']?.rush_round_set) {
                this.rush_round_set = res.data.active_more_data['active_conf_set'].rush_round_set
            }

            // #ifndef H5
            this.$refs.expirationReminder.open(res.data.active_details)
            // #endif


            const detail = res.data['active_details']
            app.globalData.activity_detail = JSON.parse(JSON.stringify(detail))

            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace rank_set.shield_other */
            /** @namespace rank_set.open_sport_moment */
            /** @namespace rank_set.exchange_gift_once */
            /** @namespace rank_set.rush_round_refresh_daily */
            let rank_set = {}
            
            if (detail.rank_set) {
                rank_set = detail.rank_set
                this.rank_set = detail.rank_set
            }

            if (!just_update && app.globalData['userid'] === detail.userid) {
                this.is_my_activity = true
            }
            
            this.detail = detail
            this.integral_unit = detail.conf.active.integral?.unit || '积分'
            my_storage.setActivityCloaeAdStorage(this.id, rank_set.closed_AD)


            if (detail.conf) {
                const conf = detail.conf

                if (conf.active) {
                    const active = conf.active
                    if (!this.screen_pic && active.screen_pic) {
                        this.screenPicShow(active.screen_pic)
                    }

                    if (active.news?.news_id) this.changeDetailContent(active.news.news_id)

                    const active_details_notice_list = this.setActiveDetailsNotice(active, rank_set)
                    this.active_details_notice_list = active_details_notice_list
                    
                    this.setRightIcon(detail, active_details_notice_list)
                    
                    if (active.start_challenge_button_set) {
                        this.start_challenge_button_set = active.start_challenge_button_set
                        delete active.start_challenge_button_set
                    }
                }

                if (conf.must_submit) {
                    const must_submit = conf.must_submit
                    delete conf.must_submit
                    if (must_submit.length) {
                        must_submit.forEach(v => v.value = v.value || '')
                        this.must_submit = must_submit
                    }
                }
                
                if (conf.AI_motion) {
                    // 把AI运动设置缓存到页面，不设置到页面data里面
                    if (conf.AI_motion.motion_list?.length) this.AI_motion_list = conf.AI_motion.motion_list
                    delete conf.AI_motion
                }
            }

            
            if (rank_set.shield_other) {
                this.$uni.hideHomeButton()
                // 更新纯净版缓存信息
                utils.updateShieldOtherInfo(this.detail)
            }


            this.addLookRecords()

            if (detail.name) this.$uni.setNavigationBarTitle(detail.name)

            return true
        },

        setRightIcon(detail, active_details_notice_list) {
            const {task_active_icons_set, article_reading} = detail?.conf?.active || {}

            let right_icons_set = []
            if (task_active_icons_set) {
                if (task_active_icons_set.length) {
                    right_icons_set = config.getTaskActiveIconsSet(task_active_icons_set)
                }
                delete detail.conf.active.task_active_icons_set
            }
            if (!right_icons_set.length) right_icons_set = config.getDefaultIconsSet()

            const rank_set = detail.rank_set || {}

            const right_icons_set__ = []

            right_icons_set.forEach(item => {
                if (item.hide) return
                if (item.name === 'set' && app.globalData['userid'] !== detail.userid) return
                if (item.name === 'exam' && !rank_set.exam_open) return
                if (item.name === 'sport_comment' && !rank_set.open_sport_moment) return
                if (item.name === 'shopping' && !rank_set.gift_goods) return
                if (item.name === 'article_reading' && !article_reading?.category_id) return
                if (item.name === 'active_notice' && !active_details_notice_list?.length) return
                if (item.name === 'lottery' && !rank_set['lottery_open']) return
                if (item.name === 'certificate' && !rank_set['diy_books']) return

                const isDiy = !!(item.type === 'diy' && item.image)

                right_icons_set__.push({
                    name: item.name,
                    image: isDiy ? item.image : '',
                    className: isDiy ? 'icon-item-image-container' : 'icon-item'
                })
            })
            this.right_icons_set = right_icons_set__
        },

        setActiveDetailsNotice(active, rank_set) {
            if (!active.active_details_notice) return []

            const set = active.active_details_notice
            if (!set?.open || !rank_set.active_details_notice) return []

            let notice_list = set.notice_list
            if (!set.notice_list) {
                notice_list = [{
                    news_id: set.news_id,
                    news_title: set.news_title,
                    confirm_text: set.confirm_text
                }]
            }

            delete active.active_details_notice

            return notice_list
        },

        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },

        async getUserStatus() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {active_id: this.id}
            })

            if (res?.data?.user_details) {
                const attend_details = res.data.user_details
                this.is_joining = true
                this.checked = attend_details.checked || 0

                if (attend_details.headimg) this.headimg = attend_details.headimg

                if (attend_details.must_submit) {
                    this.must_submit.forEach((v, i) => {
                        attend_details.must_submit.forEach(vv => {
                            if (vv.title === v.title) this.$set(this.must_submit[i], 'value', vv.value)
                        })
                    })
                }
                
                const deleteKeys = ['active_id', 'active_types', 'userid', 'must_submit', 'headimg', 'shopid', 'create_time', 'update_time']
                deleteKeys.forEach(key => {
                    if (attend_details.hasOwnProperty(key)) delete attend_details[key]
                })

                this.user_details = attend_details

                await this.getTodayIntegral()

                await this.getBmiValue()
            } else {
                this.no_attend = true
                if (this.screen_pic_show) return
                this.passwordDialogShow()
            }
        },

        async getTodayIntegral() {
            if (!this.rank_set['rushing_round_index_current_day_integral']) return

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.userActiveIntegral/active_integral_list',
                data: {
                    active_id: this.id,
                    date: this._utils.getDay(0, true),
                    total_integral: 1, // 【1】指定是否需要返回指定条件下的积分明细列表的总计积分数额，默认不返回总计的积分数额。
                    page: 1,
                    perpage: 1
                }
            })

            this.todayIntegral = res?.data?.total_integral || 0
        },

        async getBmiValue() {
            const job_list = this.detail?.conf?.active?.job_set?.job_list || []

            // 只有添加了体脂任务才显示BMI值，因为只有体脂任务需要填写身高体重，BMI要通过身高体重来计算，其他任务不一定有身高体重
            const open = job_list.some(item => item.types === 28)
            if (!open) return

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.lose_weight.user/user_weight_height_list',
                data: {
                    active_id: this.id,
                    myself: 1,
                    page: 1,
                    perpage: 1
                }
            })

            let {height = 0, weight = 0} = res?.data?.list?.data?.[0] || {}
            if (!height || !weight) return

            height /= 1000
            weight /= 1000

            const heightInMeters = height / 100
            const bmi = Number((weight / (heightInMeters * heightInMeters)).toFixed(1))

            const bmiCategory = bmi => {
                if (bmi < 18.5) return '偏瘦'
                if (bmi >= 18.5 && bmi < 24) return '正常'
                if (bmi >= 24 && bmi < 28) return '偏胖'
                if (bmi >= 28) return '肥胖'
            }

            this.bmi_value = {
                value: bmi,
                category: bmiCategory(bmi)
            }
        },

        async getLevelList() {
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_map_point_list',
                data: {active_id: this.id}
            })

            const list = res?.data?.['map_point_list'] || []
            const level_list = list.filter(item => item.types === 0)
            if (!level_list?.length) return
            this.level_list = level_list
            this.level_count = level_list.length
            this.levelNames = level_list.map(item => item.name)
        },

        async setCurrentLevel() {
            if (!this.level_list?.length) return
            let current_level_index = this.user_details?.['rushed_round'] || 0
            if (current_level_index > this.level_list.length - 1) {
                current_level_index = this.level_list.length - 1
            }
            this.current_level = this.level_list[current_level_index]
            this.current_level_index = current_level_index
            this.current_level_image = this.current_level.map_pic
            this.levelImageFixed = !!this.current_level?.conf?.['image_fixed']

            const current_level_id = this.current_level_id
            this.current_level_id = this.current_level.id
            if (current_level_id !== null && current_level_id !== this.current_level_id) {
                this.nextLevelPopupShow()
            }
        },

        nextLevelPopupShow() {
            this.task_list = []
            this.uniPopupClose('task_list')
            this.uniPopupOpen('next_level')
        },

        async certificateUnlockCheck() {
            if (!this.rank_set?.['diy_books']) return

            if (this.current_level_index === null) return

            if (this.current_level_index + 1 !== this.level_count) return

            if (!this.task_list?.length) return

            if (!this.task_list.every(item => item.is_finished)) return

            this.uniPopupClose('task_list')
            this.uniPopupOpen('lookCertificatePopup')
        },

        lookCertificate() {
            this.uniPopupClose('lookCertificatePopup')
            this.$uni.navigateTo(`/pages/activity/other/certificate?id=${this.id}&active_types=${this.detail.types}`)
        },

        currentLevelImageLoad(e) {
            const {width} = e.detail
            let {windowWidth} = uni.getSystemInfoSync()
            const scale = width / windowWidth
            this.scale = scale

            setTimeout(() => {
                this.$nextTick(() => {
                    const top = this.current_level?.margin_top
                    if (!top) return false
                    const scrollTop = top / scale

                    uni.pageScrollTo({
                        scrollTop,
                        duration: 800
                    })
                })
            }, 300)
        },

        setImageDiyTaskSet(id) {
            if (!this.rank_set?.['rushing_details_diy']) {
                this.image_diy_task_set = []
                return
            }

            const level = this.level_list.find(item => item.id === id)
            const set = level?.conf?.level_task_image_diy_set
            const task_list = set?.task_list
            if (!task_list?.length || !set?.width) {
                this.image_diy_task_set = []
                return
            }

            const {windowWidth} = uni.getSystemInfoSync()
            const scale = windowWidth / set.width
            this.image_diy_task_set = task_list.map(item => {
                return {
                    ...item,
                    width: item.width * scale + 'px',
                    height: item.height * scale + 'px',
                    x: item.x * scale + 'px',
                    y: item.y * scale + 'px'
                }
            })
        },

        async toImageDiyTask(types) {
            await this.taskListShow(this.current_level_id, true)
            const task = this.task_list.find(item => item.types === types)
            if (!task) return
            this.toCompleteTask(task)
        },


        currentLevelImageClick(e) {
            if (this.image_diy_task_set?.length) return


            const point_id = this.clickImgPoint(e.detail)
            if (point_id === false) return

            if (!this.is_joining) return this.$uni.showToast('还没有参与活动哦')
            if (!this.checked) return this.$uni.showToast('报名还没有审核通过哦')

            const click_level_index = this.level_list.findIndex(v => v.id === point_id)
            const current_level_index = this.user_details?.['rushed_round'] || 0
            if (click_level_index > current_level_index) 
                return this.$uni.showToast(`第${click_level_index + 1}关 还没有解锁哦`)
            
            if (this.rank_set.rush_round_refresh_daily && click_level_index < current_level_index) {
                // 鲜繁客户定制开发，【rush_round_refresh_daily】OA开启了每天刷新任务。需要不能查看已经闯过的关卡。
                // 仅通过开启【rush_round_refresh_daily】来判断，之前是可以随意点开以前的关卡的，还是可以点开，只有OA开了【rush_round_refresh_daily】才提示已完成，不能查看
                // 仅通过开启【rush_round_refresh_daily】来判断是因为，逻辑就和健步走一样的，已经解锁的点位可以点开来（查看点位知识也好，答题也好）。
                // 因为鲜繁这个定制开发每天刷新任务的情况不一样，所以单独判断不能点开以前大关卡
                return this.$uni.showToast(`第${click_level_index + 1}关 已完成`)
            }

            this.taskListShow(point_id)
        },

        clickImgPoint(detail) {
            if (!detail || !detail.x || !detail.y || !this.level_list.length) return false

            const level_list = this.level_list

            for (let i = 0; i < level_list.length; i++) {
                const item = level_list[i]
                if (
                    item.margin_top !== '' &&
                    item.conf?.point_img_detail &&
                    item.conf.point_img_detail.margin_left !== '' &&
                    item.conf.point_img_detail.point_height !== '' &&
                    item.conf.point_img_detail.point_width !== ''
                ) {
                    const top = item.margin_top,
                        left = item.conf.point_img_detail.margin_left,
                        width = item.conf.point_img_detail.point_width,
                        height = item.conf.point_img_detail.point_height
                    if (this.isInPoint(detail.x, detail.y, top, left, width, height)) {
                        return item.id
                    }
                }
            }
            return false
        },

        isInPoint(x, y, top, left, width, height) {
            x *= this.scale
            y *= this.scale
            return x >= left && x <= left + width && y >= top && y <= top + height
        },

        goNext() {
            this.uniPopupClose('next_level')
            this.GO()
        },

        joinOrStart() {
            // 鲜繁定制开发，神木的，需要单独一个页面显示任务列表
            if (this.id === 'e500dedf72811dfd04d474c3ceb1ee5c') return this.shenMuGo()

            // 这个鲜繁的客户需要点击“去闯关”按钮时报名
            if (!this.is_joining && this.id === '5060debc4b8f6a9ad9d799ee77ae7fb3') {
                return this.joinActivity()
            }

            this.GO()
            /*if (this.is_joining) return this.GO()
            this.joinActivity()*/
        },

        shenMuGo() {
            // 不想在单独的任务列表页面判断是否报名，所以这里没有报名的话先弹出报名窗口
            if (!this.joinCheck()) return

            this.shenMuGoList()
        },

        shenMuGoList() {
            const current_level = this.current_level
            if (!current_level) return this.$uni.showToast('没有获取到你的关卡信息')
             this.$uni.navigateTo(`./task-list?active_id=${this.id}&level_id=${current_level.id}`, {
                 success: res => {
                     const data = {
                         activeDetails: this.detail,
                         levelDetails: this.level_list.find(item => item.id === current_level.id),
                         AI_motion_list: this.AI_motion_list,
                         taskPopupStyle: this.taskPopupStyle,
                         sportMomentName: this.sportMomentName,
                         taskListShowShopSet: this.taskListShowShopSet,
                         rush_round_set: this.rush_round_set || {},
                     }
                     res.eventChannel.emit('data', data)
                 },
                 events: {
                     success: () => this.reLoadData()
                 }
             })
        },

        getBodyDataSubmitTask(task_list) {
            if (this.detail.conf.active.body_data_submit_task_merge_set?.merge) {
                return task_list.find(item => item.types === 'body') || null
            }

            const taskList = task_list.filter(item => [27, 28, 29].includes(item.types))
            if (!taskList.length) return null
            return taskList[0]
        },

        bodyDataSubmitTaskTips() {
            if (!this.is_joining) return

            const {body_data_submit_task_first_tips: first, body_data_submit_task_last_tips: last} = this.detail.conf.active

            if (!first?.open && !last?.open) return

            const task_list = this.task_list || []
            if (!task_list.length) return

            const task = this.getBodyDataSubmitTask(task_list)
            if (task === null) return

            const submitCount = task.finished_details?.finished_details || 0

            if (submitCount === 0 && first?.open) {
                return this.$uni.showModal(first.tips || '', {
                    showCancel: true,
                    confirmText: '去完成',
                    success: res => res.confirm && this.toCompleteTask(task)
                })
            }

            if (submitCount === 1 && last?.open) {
                const activeLastDay = this._utils.unitTimeToDate(this.detail.end_time * 1000)
                const lastTime = new Date(activeLastDay).getTime()
                const todayTime = new Date(this._utils.getDay()).getTime()
                if (lastTime < todayTime) return

                const leftDays = Math.ceil((lastTime - todayTime) / 86400000)
                const days = last.days || 0
                if (leftDays <= days) return this.$uni.showModal(last.tips || '', {
                    showCancel: true,
                    confirmText: '去完成',
                    success: res => res.confirm && this.toCompleteTask(task)
                })
            }
        },

        async GO() {
            // if (!this.is_joining) return this.$uni.showToast('还没有参与活动哦')
            // if (!this.checked) return this.$uni.showToast('报名还没有审核通过哦')
            const current_level = this.current_level
            if (!current_level) return this.$uni.showToast('没有获取到你的关卡信息')
            await this.taskListShow(current_level.id)

            this.$nextTick(() => this.bodyDataSubmitTaskTips())
        },


        async taskListShow(point_id, update = false) {
            if (!update) {
                this.task_list = []
                this.level_task_description = ''
                this.justOneTask ? this.$uni.showLoading() : this.uniPopupOpen('task_list')
            }
            this.task_list_pointid = point_id
            this.task_list_level_number = this.level_list.findIndex(v => v.id === point_id) + 1


            this.task_list_loading = true
            const res = await xwy_api.request({
                url: 'front.flat.sport_step.job_list.jobState/job_finished_state_list',
                data: {
                    active_id: this.id,
                    point_id
                }
            })
            this.task_list_loading = false

            if (!update && this.justOneTask) uni.hideLoading()


            if (res?.status !== 1) {
                this.uniPopupClose('task_list')
                
                if (res?.data?.must_attend) {
                    this.joinActivity()
                    return
                }
                
                this.$uni.showModal(res?.info || '任务获取失败')
                return
            }
            const task_list = res?.data?.['job_finished_check']?.job_list || []
            if (!task_list?.length) {
                this.uniPopupClose('task_list')
                return this.$uni.showToast('任务获取失败', 'error')
            }

            this.task_list = this.taskListDataInit(task_list)


            this.setLevelTaskListDescription(res?.data?.['job_finished_check']?.finished_num)


            // 每日刷新任务，只有一个关卡，只有一个任务，直接进入到任务
            if (!update && this.justOneTask) this.toCompleteTask(this.task_list[0])

        },

        setLevelTaskListDescription(finished_num) {
            if (this.justOneLevelNotLevel) return  // 只有一关默认为无需关卡，不显示过关描述
            
            
            const clearance_type = this.detail.conf.active.job_set?.types || 1
            const description_options = {
                1: () => {
                    const must_finished_num = this.detail.conf.active.job_set?.must_finished_num || 0
                    const task_count = this.task_list.length
                    let description = must_finished_num === task_count ?
                        '完成下列任务后解锁下一关' :
                        `以下任务中完成${must_finished_num}个即可解锁下一关`

                    if (finished_num) description += ` (已完成${finished_num}个)`
                    return description
                },
                2: () => {
                    // 只有一关不显示
                    if (this.level_count === 1) return ''

                    const level = this.level_list.find(v => v.id === this.task_list_pointid)
                    if (!level) return ''
                    const {max_num = 0} = level
                    const unit = this.integral_unit
                    let description = `超过${max_num}${unit}解锁下一关`
                    if (this.current_level_index === this.level_list.length - 1)
                        description = '恭喜你已解锁所有关卡'
                    // if (this.is_joining) {
                    //     const {integral_all = 0} = this.user_details
                    //     description += ` (已获得${integral_all}${unit})`
                    // }
                    return description
                }
            }

            this.level_task_description = description_options[clearance_type]?.() || ''
        },

        taskListDataInit(task_list) {
            const {job_set = {}, integral = {}, task_active_ai_sport_task_merge, body_data_submit_task_merge_set, body_data_submit_task_last_tips} = this.detail.conf.active
            const {unit: integral_unit = '积分', exam_reward_num} = integral

            const {AI_motion, rush_round_refresh_daily} = this.rank_set

            const level = this.level_list.find(v => v.id === this.task_list_pointid)

            const {point_job_set_open = 0, job_set: {job_list = []} = {}} = level.conf || {}

            // 如果关卡设置了自定义任务，过滤掉点位没有选择的任务
            if (point_job_set_open && job_list.length) {
                task_list = task_list.filter(item => job_list.some(v => v.types === item.types))
            }

            let taskList = task_list.map(v => {
                // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
                /** @namespace v.finished_details.have_finished */
                
                // 鲜繁客户定制活动（判断开启了rush_round_refresh_daily， 每天刷新任务状态）。答题任务积分数显示
                if (rush_round_refresh_daily && v.types === 2 && Number(exam_reward_num)) {
                    v.integral = exam_reward_num
                }

                let is_finished = false
                if (v.finished_details) {
                    // 开启了提示用户提交最后一次身体记录，用户记录了一次，并且没到提示时间，要把任务变成已完成
                    const taskTypes = [27, 28, 29]
                    if (taskTypes.includes(v.types) && body_data_submit_task_last_tips?.open) {
                        // 活动结束日期
                        const activeLastDay = this._utils.unitTimeToDate(this.detail.end_time * 1000).replace(/-/g, '/')
                        // 活动结束时间戳
                        const lastTime = new Date(activeLastDay).getTime()
                        // 今日0点时间戳
                        const todayTime = new Date(this._utils.getDay(0, true, '/')).getTime()

                        // 活动未结束
                        if (lastTime > todayTime) {
                            // 活动还有多少天结束
                            const leftDays = Math.ceil((lastTime - todayTime) / 86400000)
                            // 设置的距离活动多少天提示去完成任务
                            const days = body_data_submit_task_last_tips.days || 0

                            // 如果还没到提示时间
                            if (leftDays > days) {
                                // 任务未完成，要标记成已完成
                                if (v.finished_details.finished_details === 1) {
                                    v.finished_details.finished_details = 2
                                    v.finished_details.have_finished = true
                                }
                            }
                        }
                    }


                    if (v.finished_details.have_finished) is_finished = true

                    // 运动步数按积分数闯关，如果有item.max，并且item.finished_details.today_have_exchange大于等于item.max，需要显示已完成,反之则显示未完成
                    if (job_set.types === 2 && v.types === 1 && v.max) {
                        const today_exchange_step = Number(v.finished_details.today_have_exchange || 0)
                        is_finished = today_exchange_step >= v.max
                    }
                }

                v.is_finished = is_finished
                
                // 是不是 按积分数过关(job_set.types === 2) 并且是 步数兑换任务(v.types === 1)
                const types_2_1 = (job_set.types === 2 && v.types === 1)

                /*// 按积分数过关，步数兑换未完成显示为"去运动"
                v.complete_button_text = is_finished ? '已完成' : (types_2_1 ? '去运动' : '去完成')*/

                // 按积分数过关，步数兑换未完成默认显示为"去运动"
                if (!v.not_complete_button_text && !is_finished && types_2_1) v.not_complete_button_text = '去运动'

                v.complete_button_text = is_finished ? (v.complete_button_text || '已完成') : (v.not_complete_button_text || '去完成')

                if (types_2_1 && v.max && v.min && v.integral) {
                    // 按积分数过关，步数兑换任务，如果有设置每日步数上限，页面显示的积分数为每日步数上限可获得的积分数
                    v.integral = Math.floor(v.max / v.min * v.integral)
                }

                const daily_sign = this.detail.conf.active.daily_sign
                
                if (v.types === 6 && daily_sign) {
                    const {circle_set, integral, types} = daily_sign
                    v.integral = types === 1 ? integral : circle_set[0].integral
                }
                
                // 填词任务，设置每题奖励积分
                if (v.types === 9) {
                    if (v.hasOwnProperty('per_integral')) v.integral = v.per_integral
                    if (!level?.conf?.fill_words?.category_id) v.hidden = true
                }
                if (v.finished_details?.['round_job_set']) delete v.finished_details['round_job_set']
                
                
                // 关卡没有绑定答题，不显示
                if (v.types === 2 && !level?.exam_id) if (!level?.exam_id) v.hidden = true

                // 关卡没有绑定找茬关卡，不显示
                if (v.types === 14 && !level?.conf?.picture_diff?.id) v.hidden = true
                
                // 只能管理员添加体重记录，任务不显示给用户看
                if (this.detail.conf.active.admin_submit_body_data && [27, 28, 29].includes(v.types)) v.hidden = true

                v.describe = this.getTaskDescribe(v)

                return v
            })


            // 合并身体数据提交任务
            if (body_data_submit_task_merge_set?.merge) {
                const taskTypes = [27, 28, 29]
                const insertIndex = taskList.findIndex(item => taskTypes.includes(item.types))
                if (insertIndex !== -1) {
                    const task_list = taskList.filter(item => taskTypes.includes(item.types))

                    taskList.splice(insertIndex, 0, {
                        types: 'body',
                        title: body_data_submit_task_merge_set.set?.title || '',
                        logo: body_data_submit_task_merge_set.set?.logo || '',
                        text: body_data_submit_task_merge_set.set?.text || '',
                        finished_details: task_list[0].finished_details,
                        is_finished: task_list[0].is_finished,
                        complete_button_text: task_list[0].complete_button_text,
                        type_list: task_list.map(item => item.types),
                        hidden: this.detail.conf.active.admin_submit_body_data
                    })

                    taskList = taskList.filter(item => !taskTypes.includes(item.types))
                }
            }
            
            /*
            * 鲜繁客户定制活动，需要把每一项AI运动显示在任务列表里面
            * */
            if (!AI_motion) return taskList  // 没有开启AI运动
            if (task_active_ai_sport_task_merge) return taskList  // 开启了合并AI运动任务

            const aiSportTaskIndex = taskList.findIndex(item => item.types === 4)
            if (aiSportTaskIndex === -1) return taskList  // 没有配置AI运动任务

            const aiSportTask = JSON.parse(JSON.stringify(taskList[aiSportTaskIndex]))
            taskList.splice(aiSportTaskIndex, 1) // 把原来的AI运动集合任务删除

            let AI_sport_list = JSON.parse(JSON.stringify(this.AI_motion_list))
            const types_list = level.conf?.ai_sport?.types_list
            if (types_list?.length) {
                // 关卡自定义选择AI运动
                AI_sport_list = AI_sport_list.filter(item => types_list.includes(item.types))
            }
            if (!AI_sport_list?.length) return taskList  // 没有配置AI运动

            const reward_integral_list = aiSportTask.finished_details?.['sport_reward_integral_list'] || []

            AI_sport_list.forEach((item, index) => {
                const task = {
                    types: 4,
                    title: item.name,
                    sport_types: item.types,
                    logo: item.logo || ''
                }
                
                let describe = '', is_finished = false
                
                if (item.integral_reward) {
                    const {integral, max_daily, num} = item.integral_reward
                    task.integral = max_daily || integral || 0
                    if (num && integral) {
                        const isCounts = this.xwy_config.AiSportNeedCountsTypes(item.types)
                        const sport_unit = isCounts ? '个' : '秒'
                        describe = `${item.name}${num}${sport_unit}奖励${integral}${integral_unit}`
                    }
                    if (max_daily) {
                        describe += `, 每日上限${max_daily}${integral_unit}`
                        const today_reward_integral = reward_integral_list.find(sport => sport.sport_types === item.types)?.['integral_num'] || 0
                        if (today_reward_integral >= Number(max_daily)) is_finished = true
                    }
                }
                
                task.describe = item.describe || describe
                task.is_finished = is_finished
                task.complete_button_text = is_finished ? (aiSportTask.complete_button_text || '已完成') : (aiSportTask.not_complete_button_text || '去完成')

                const insert_index = aiSportTaskIndex + index
                taskList.splice(insert_index, 0, task)
            })

            return taskList
        },

        getTaskDescribe(v) {
            const unit = this.detail.conf.active.integral?.unit || '积分'

            const completeRewardTypes = [8, 10, 11, 13, 14, 19, 20, 21, 25, 30, 31, 32, 33, 34]
            if (completeRewardTypes.includes(v.types)) return `完成任务奖励${v.integral}${unit}`

            const describeOptions = {
                1: () => `当日步数达到${v.min}奖励${v.integral}${unit}`,
                2: () => {
                    const {exam_reward_num, exam_reward} = this.detail.conf.active.integral || {}
                    if (exam_reward_num && exam_reward) {
                        return `答题每获得${exam_reward}分奖励${exam_reward_num}${unit}`
                    }
                    return ''
                },
                3: () => `每发布1条动态奖励${v.integral}${unit}`,
                5: () => `阅读奖励${unit}`,
                6: () => {
                    const daily_sign = this.detail.conf.active.daily_sign
                    if (!daily_sign) return ''
                    const {circle_set, integral, types} = daily_sign
                    if (types === 1) return `每日签到奖励${integral}${unit}`
                    return `按照连续签到天数奖励${circle_set[0].integral}到${circle_set[circle_set.length - 1].integral}${unit}`
                },
                7: () => `完成拼图奖励${unit}`,
                9: () => {
                    if (v.hasOwnProperty('per_integral')) {
                        return `每答对1题奖励${v.per_integral || 0}${unit}`
                    }
                    return `完成任务奖励${v.integral || 0}${unit}`
                },
                12: () => {
                    if (v.hasOwnProperty('per_integral')) {
                        return `接到1个奖励${v.per_integral || 0}${unit}`
                    }
                    return `完成任务奖励${v.integral || 0}${unit}`
                },
                15: () => `接龙一次奖励${v.per_integral}${unit}`,
                16: () => `打中一只奖励${v.per_integral}${unit}`,
                18: () => {
                    if (v.hasOwnProperty('per_integral')) {
                        return `每答对1题奖励${v.per_integral || 0}${unit}`
                    }
                    return `完成任务奖励${v.integral || 0}${unit}`
                },
                22: () => `每连一个红包奖励${v.per_integral || 0}${unit}`,
                23: () => `每打中一个年兽奖励${v.per_integral || 0}${unit}`,
                24: () => `每击中一架飞机奖励${v.per_integral || 0}${unit}`,
                26: () => `每消除一组奖励${v.per_integral || 0}${unit}`,
                28: () => {
                    let info = `体脂每减少${v.num}%, 奖励${v.integral}${unit}`
                    if (v.max_integral) info += `, 奖励上限${v.max_integral}${unit}`
                    return info
                },
                29: () => {
                    let info = `腰围每减少${v.num}cm, 奖励${v.integral}${unit}`
                    if (v.max_integral) info += `, 奖励上限${v.max_integral}${unit}`
                    return info
                },
                36: () => `每数1张钱奖励${v.per_integral || 0}${unit}`,
                37: () => {
                    const accuracy = this.detail.conf.active?.tongue_twister?.accuracy || 0
                    if (accuracy) return `朗读准确率达到${accuracy}%, 奖励${v.integral || 0}${unit}`
                    return `完成任务奖励${v.integral || 0}${unit}`
                },
                38: () => `每进1球奖励${v.per_integral || 0}${unit}`,
                39: () => `每奔跑1米奖励${v.per_integral || 0}${unit}`,
                40: () => `每消除1个元素奖励${v.per_integral || 0}${unit}`,
                41: () => `每打爆1个气球奖励${v.per_integral || 0}${unit}`,
                42: () => `每跳过1个平台奖励${v.per_integral || 0}${unit}`,
                43: () => `每前进1米奖励${v.per_integral || 0}${unit}`,
                45: () => `每发布1条动态奖励${v.integral}${unit}`,
                46: () => {
                    if (this.detail.conf.active.bo_bing?.reward_type === 2) return ''
                    return `骰子每摇出1点奖励${v.per_integral}${unit}`
                },
                47: () => `每抓住1个月饼奖励${v.per_integral}${unit}`
            }

            return describeOptions[v.types]?.() || ''
        },


        taskTimeCheck() {
            let {begin_time, end_time} = this.detail.conf.active.job_set || {}
            if (!begin_time && !end_time) return true

            begin_time ||='00:00:00'
            end_time ||= '23:59:59'
            
            const now = new Date().getTime()
            const today = this._utils.getDay(0, true, '/')
            const begin = new Date(`${today} ${begin_time}`).getTime()
            const end = new Date(`${today} ${end_time}`).getTime()
            if (now < begin) {
                this.$uni.showToast(`${begin_time} 后才能开始任务`)
                return false
            }
            if (now > end) {
                this.$uni.showToast(`${end_time} 后不能做任务, 请明天 ${begin_time} 后再来`)
                return false
            }
            
            return true
        },

        activeTimeCheck() {
            const now_time = new Date().getTime()
            const {begin_time, end_time} = this.detail

            if (begin_time * 1000 > now_time) {
                const date = this._utils.unitTimeToDate(begin_time * 1000, true)
                this.$uni.showModal(`活动未开始。活动开始时间为${date}`)
                return false
            }
            if (end_time * 1000 < now_time) {
                const date = this._utils.unitTimeToDate(end_time * 1000, true)
                this.$uni.showModal(`活动已结束。活动结束时间为${date}`)
                return false
            }
            
            return true
        },

        joinCheck(item = null) {
            if (!this.is_joining) {
                // 体验版不报名直接进入的任务详情，报名弹窗点直接进入的时候需要知道是什么任务
                if (item) this.trial_no_join_just_go_task_item = item
                this.joinActivity()
                return false
            }
            if (!this.checked) {
                this.$uni.showToast('报名还没有审核通过哦')
                return false
            }
            
            return true
        },


        trialNoJoinJustGoTask() {
            // 鲜繁定制开发，神木的，需要单独一个页面显示任务列表
            if (this.id === 'e500dedf72811dfd04d474c3ceb1ee5c') return this.shenMuGoList()

            if (!this.trial_no_join_just_go_task_item) return this.$uni.showToast('没有指定任务')
            this.toCompleteTask(this.trial_no_join_just_go_task_item, true)
        },


        toCompleteTask(item, trial_no_join_just_go_task = false) {
            /*// 雷子的客户活动，任务已完成不能再进入
            const cannot_enter_after_completion = this.id === '5dc87ddf3ed74207006dc2772ad013c6'
            if (item.is_finished && cannot_enter_after_completion) return this.$uni.showToast('任务已完成')*/

            // 所有活动，任务完成了不能继续进入
            if (item.is_finished && !trial_no_join_just_go_task) return this.$uni.showToast('任务已完成')

            const point_id = this.task_list_pointid
            const level = this.level_list.find(v => v.id === point_id)
            if (!level) return this.$uni.showToast('关卡获取失败', 'error')

            // 体验版直接进入不判断这些东西
            if (trial_no_join_just_go_task !== true) {
                if (!this.joinCheck(item)) return
                if (!this.activeTimeCheck()) return
                if (!this.taskTimeCheck()) return
            }
            
            
            const {exam_open, open_sport_moment, AI_motion, reading_reward, closed_AD} = this.rank_set

            const all_count = item.count
            const submit_count = item['finished_details']?.count
            if (all_count && submit_count && submit_count >= all_count)
                return this.$uni.showToast('任务已完成')
            
            const jump = (url, options) => {
                this.$uni.navigateTo(url, {
                    events: {
                        success: () => this.successCheck(point_id)
                    },
                    ...options
                })
            }

            const types = item.types

            if (types === 1) return this.toCompleteStepTask(item)

            if (types === 2) {
                // 去答题
                if (!exam_open) return this.$uni.showToast('活动未开启答题功能')
                const exam_id = level.exam_id
                if (!exam_id) return this.$uni.showToast('关卡未绑定答题')
                
                return jump(`/pages/likou_dati/pages/exam/exam_details/exam_details?exam_id=${exam_id}&active_id=${this.id}&point_id=${point_id}&rush_round=1`)
            }

            if (types === 3 || types === 45) {
                if (!open_sport_moment) return this.$uni.showToast('活动未开启运动圈功能')
                return this.toCompleteSportMomentTask(point_id, types, jump)
            }

            if (types === 4) {
                if (!AI_motion) return this.$uni.showToast('活动未开启AI运动功能')


                // 不合并AI运动任务，所有AI运动类型展示出来，有sport_types的就是AI运动项目
                if (item.sport_types) {
                    let url = `/pages/ai_sport/pages/ai-sport-recognition?active_id=${this.id}&point_id=${point_id}&types=${item.sport_types}&name=${item.title}`
                    if (this.rank_set['ai_hand_start']) url += '&is_manual=1'

                    return jump(url)
                }

                let url = `/pages/ai_sport/pages/list?active_id=${this.id}&point_id=${point_id}&is_task=1`
                if (this.rank_set['ai_hand_start']) url += '&manual=1'
                return jump(url, {
                    success: res => {
                        let list = JSON.parse(JSON.stringify(this.AI_motion_list))
                        const types_list = level.conf?.ai_sport?.types_list
                        if (types_list?.length) {
                            // 关卡自定义选择AI运动
                            list = list.filter(item => types_list.includes(item.types))
                        }
                        res.eventChannel.emit('AI_motion_list', list || [])
                    }
                })
            }

            if (types === 5) {
                if (!reading_reward) return this.$uni.showToast('活动未开启阅读奖励功能')
                let url = `/pages/news/list?active_id=${this.id}&point_id=${point_id}&just_look_active=1&show_is_read=1`
                if (closed_AD) url += `&vip=1`
                const category_list = this.detail.conf.active.reading_reward?.rules
                if (!category_list?.length) return this.$uni.showToast('活动未设置奖励文章')
                const list = category_list.map(v => {
                    return {
                        id: v.category_id,
                        n: v.name,
                        s: Math.floor(v.seconds),
                        i_min: Number(v.integral?.min || 0) || 0,
                        i_max: Number(v.integral?.max || 0) || 0
                    }
                })
                url += `&category_list=${JSON.stringify(list)}`
                return jump(url)
            }
            
            if (types === 6) return jump(`/pages/sign_in/sign_in?active_id=${this.id}`)
            
            if (types === 7) {
                let url = `/pages/puzzle/puzzle-list?active_id=${this.id}&point_id=${point_id}`
                if (!closed_AD) url += '&show_ad=1'
                const json_set = this.rush_round_set?.['puzzle_image']
                if (json_set) {
                    const {bg_img, navigation_bar} = json_set
                    if (bg_img) url += `&bg_img=${bg_img}`
                    if (navigation_bar) {
                        const {bg_color, font_color} = navigation_bar
                        if (bg_color) url += `&nav_bg_color=${bg_color}`
                        if (font_color) url += `&nav_font_color=${font_color}`
                    }
                }
                if (this.rush_round_set?.['puzzle_image']?.bg_img) url += `&bg_img=${this.rush_round_set['puzzle_image'].bg_img}`
                return jump(url)
            }
            
            if (types === 8) return jump(`/pages/historical-event-sort/user/sorting?active_id=${this.id}&point_id=${point_id}`)
            
            if (types === 9) {
                const {num = 0, category_id, type} = level.conf?.fill_words || {}

                let url = '/pages/fill-words/fill-words'
                if (type === 'fill_couplet') url = '/pages/fill-words/fill-couplet'
                url += `?active_id=${this.id}&point_id=${point_id}&category_id=${category_id}&num=${num}`
                if (!closed_AD) url += '&show_ad=1'
                if (item.title) url += `&title=${item.title}`

                if (type === 'fill_words' && this.rush_round_set?.fill_words?.bg_img) {
                    url += `&bg_img=${this.rush_round_set.fill_words.bg_img}`
                }
                return jump(url, {
                    success: res => {
                        const imageSet = this.rush_round_set?.fill_words
                        if (type === 'fill_couplet' && imageSet) res.eventChannel.emit('imageSet', imageSet)
                    }
                })
            }

            if (types === 10) {
                let url = `/pages/red-small-games/mine-sweeper/game?active_id=${this.id}&point_id=${point_id}`
                
                if (level.conf?.mine_sweeper) {
                    const {row, col, boom_num, seconds} = level.conf.mine_sweeper
                    url += `&row=${row}&col=${col}&boom_num=${boom_num}`
                    if (seconds) url += `&seconds=${seconds}`
                }

                if (!closed_AD) url += '&show_ad=1'
                if (this.rush_round_set?.['mine_clearing_bom']?.bg_img) url += `&bg_img=${this.rush_round_set['mine_clearing_bom'].bg_img}`
                
                if (item.title) url += `&title=${item.title}`
                return jump(url)
            }

            if (types === 11) {
                return jump('/pages/red-small-games/lian-lian-kan/game', {
                    success: res => {
                        const data = {
                            active_id: this.id,
                            point_id,
                            integral: item.integral || 0
                        }

                        if (!closed_AD) data.show_ad = 1
                        if (item.title) data.title = item.title

                        if (level.conf?.lian_lian_kan) {
                            const {row, col, img_count, seconds} = level.conf.lian_lian_kan
                            data.row = row
                            data.col = col
                            data.img_count = img_count
                            if (seconds) data.seconds = seconds
                        }

                        const json_set = this.rush_round_set?.['link_game']
                         if (json_set) {
                             const {bg_img, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 12) {
                let url = `/pages/red-small-games/grab-grain/game?active_id=${this.id}&point_id=${point_id}`

                if (this.detail?.conf?.active?.grab_grain_game_set) {
                    const {target_grain, seconds} = this.detail.conf.active.grab_grain_game_set
                    if (target_grain) url += `&target_grain=${target_grain}`
                    if (seconds) url += `&seconds=${seconds}`
                    if (item.per_integral) url += `&per_integral=${item.per_integral}`
                }

                if (!closed_AD) url += '&show_ad=1'
                
                if (item.title) url += `&title=${item.title}`
                
                return jump(url, {
                    success: res => {
                        const jsonSet = this.rush_round_set?.['grab_grain_game']
                        if (jsonSet) res.eventChannel.emit('jsonSet', jsonSet)
                    }
                })
            }

            if (types === 13) {
                let {count, seconds, level: game_level} = config.gluttonousSnakeDefaultSet
                const level_set = level.conf?.snake
                if (level_set) {
                    if (level_set.count) count = level_set.count
                    if (level_set.seconds) seconds = level_set.seconds
                    if (level_set.level) game_level = level_set.level
                }

                let url = `/pages/games/gluttonous-snake/gluttonous-snake?active_id=${this.id}&point_id=${point_id}&count=${count}&seconds=${seconds}&level=${game_level}`

                if (!closed_AD) url += '&show_ad=1'
                if (item.title) url += `&title=${item.title}`

                return jump(url)
            }

            if (types === 14) {
                const {id, time} = level.conf.picture_diff
                let url = `/pages/treasure-hunt/user/look-for?active_id=${this.id}&point_id=${point_id}&id=${id}&unit=${this.detail.conf.active.integral?.unit || '积分'}&game_type=1&from_task=1`
                if (time) url += `&time=${time}`

                return jump(url)
            }

            if (types === 15) {
                let url = `/pages/games/idiom-solitaire/idiom-solitaire?id=${this.id}&point_id=${point_id}&seconds=${item.seconds || 30}&per_integral=${item.per_integral}`
                if (item.max_count) url += `&max_count=${item.max_count}`
                if (!closed_AD) url += '&show_ad=1'
                if (item.title) url += `&title=${item.title}`

                const {idiom_unison_open, idiom_tips_count} = this.detail.conf?.active || {}
                if (idiom_unison_open) url += '&unison=1'
                if (idiom_tips_count) url += `&idiom_tips_count=${idiom_tips_count}`

                return jump(url)
            }

            if (types === 16) {
                let url = `/pages/games/dds/dds?id=${this.id}&point_id=${point_id}&seconds=${item.seconds || 30}&from_task=1`
                if (!closed_AD) url += '&show_ad=1'
                if (item.title) url += `&title=${item.title}`

                return jump(url, {
                    success: res => {
                        const hit_mouse = this.rush_round_set?.['hit_mouse']
                        if (hit_mouse) res.eventChannel.emit('imageSet', hit_mouse)
                    }
                })
            }

            if (types === 18) {
                const garbage_count = this.detail?.conf?.active?.garbage_count || 10

                return jump('/pages/games/garbage-sorting/garbage-sorting', {
                    success: res => {
                        const data = {
                            id: this.id,
                            point_id,
                            seconds: item.seconds || 30,
                            garbage_count
                        }

                        if (item.integral) data.integral = item.integral
                        if (item.per_integral) data.per_integral = item.per_integral
                        if (!closed_AD) data.show_ad = 1
                        if (item.title) data.title = item.title

                        const json_set = this.rush_round_set?.['garbage_sorting']
                         if (json_set) {
                             const {bg_img, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 19) {
                return jump(`/pages/comment/list?active_id=${this.id}&point_id=${point_id}&share_task=1&not_publish=1&sport_moment_name=${this.sportMomentName}`)
            }

            if (types === 20) {
                const id = this.detail?.conf?.active?.share_reward_news?.id
                if (!id) return this.$uni.showToast('文章未配置')
                return jump(`/pages/news/preview?id=${id}&active_id=${this.id}&point_id=${point_id}&share_reward=1`)
            }

            if (types === 21) {
                let url = `/pages/games/picture-composition?active_id=${this.id}&point_id=${point_id}`
                if (item.title) url += `&title=${item.title}`
                return jump(url)
            }

            if (types === 22) {
                let url = `/pages/games/one-stroke-line/game?active_id=${this.id}&point_id=${point_id}`
                if (item.title) url += `&title=${item.title}`
                return jump('/pages/games/one-stroke-line/game', {
                    success: res => {
                        const {row = 6, col = 6, obstacle = 3} = this.detail?.conf?.active?.one_stroke_line || {}
                        const data = {
                            active_id: this.id,
                            point_id,
                            row,
                            col,
                            obstacle
                        }
                        if (item.title) data.title = item.title
                        if (item.per_integral) data.per_integral = item.per_integral
                        if (item.seconds) data.seconds = item.seconds
                        if (!closed_AD) data.show_ad = 1

                        const json_set = this.rush_round_set?.one_stroke_line
                         if (json_set) {
                             const {bg_img, start_img, end_img, fu_img, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (start_img) data.start_img = start_img
                             if (end_img) data.end_img = end_img
                             if (fu_img) data.fu_img = fu_img
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 23) {
                const second = item.seconds || 30
                let url = `/pages/games/play-nian/play-nian?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&second=${second}&per_integral=${item.per_integral}`
                if (item.title) url += `&title=${item.title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const data = {}
                        
                        const json_set = this.rush_round_set?.play_nian
                         if (json_set) {
                             const {bg_img, rocket_img, monster_img, boom_img, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (rocket_img) data.rocket_img = rocket_img
                             if (monster_img) data.monster_img = monster_img
                             if (boom_img) data.boom_img = boom_img
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 24) {
                const second = item.seconds || 30
                let url = `/pages/games/aircraft-battle/aircraft-battle?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&second=${second}&per_integral=${item.per_integral}`
                if (item.title) url += `&title=${item.title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url)
            }

            if (types === 25) {
                const second = item.seconds || 30
                const memory_time = this.detail.conf.active.fan_fan_le_memory_time || 5
                let url = `/pages/games/fan-fan-le/fan-fan-le?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&second=${second}&integral=${item.integral}&memory_time=${memory_time}`
                if (item.title) url += `&title=${item.title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const data = {}
                        
                        const json_set = this.rush_round_set?.fan_fan_le
                         if (json_set) {
                             const {bg_img, bg_color, card_bg_img, card_imgs, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (bg_color) data.bg_color = bg_color
                             if (card_bg_img) data.card_bg_img = card_bg_img
                             if (card_imgs?.length) data.card_imgs = card_imgs
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 26) {
                const second = item.seconds || 30
                let url = `/pages/games/sheep-and-sheep/sheep-and-sheep?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&second=${second}&per_integral=${item.per_integral}`
                if (item.title) url += `&title=${item.title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const data = {}
                        
                        const json_set = this.rush_round_set?.sheep
                         if (json_set) {
                             const {bg_img, card_imgs, stuck_slot_img, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (card_imgs?.length) data.card_imgs = card_imgs
                             if (stuck_slot_img) data.stuck_slot_img = stuck_slot_img
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 'body' || [27, 28, 29].includes(types)) return this.toBodyDataSubmitTask(item)

            if (types === 30) {
                return jump(`/pages/cloud_wish/user/detail?id=${this.id}&point_id=${point_id}&task_active=1`)
            }

            if (types === 32) return this.toCompleteWeekStepTask(item.step_num)

            if ([31, 33, 34].includes(types)) {
                let url = `/pages/sport_clock_in/user/sign?sport_name=${item.title}&active_id=${this.id}&types=${types}&point_id=${point_id}&button_text=${item.not_complete_button_text || ''}`

                // 任务绑定文章需要先显示文章
                const {types_31_news, types_33_news, types_34_news} = this.detail.conf.active
                if (types === 31 && types_31_news?.news_id) url += `&news_id=${types_31_news.news_id}`
                if (types === 33 && types_33_news?.news_id) url += `&news_id=${types_33_news.news_id}`
                if (types === 34 && types_34_news?.news_id) url += `&news_id=${types_34_news.news_id}`

                // 库存设置
                if (item.job_stock) {
                    const job_period = item.job_period || 7
                    url += `&job_stock=${item.job_stock}&job_period=${job_period}`
                }
                return jump(url)
            }

            if (types === 36) {
                const second = item.seconds || 30
                let url = `/pages/games/counting-money/game?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&second=${second}&per_integral=${item.per_integral}`
                if (item.title) url += `&title=${item.title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const data = {}
                        
                        const json_set = this.rush_round_set?.money
                         if (json_set) {
                             const {bg_img, money_imgs, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (money_imgs?.length) data.money_imgs = money_imgs
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 37) {
                const {title, integral, seconds} = item
                const {text_list, accuracy = 0} = this.detail.conf.active.tongue_twister

                if (!text_list?.length) return this.$uni.showToast('活动未设置绕口令内容')

                let url = `/pages/games/speech-recognition/game?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}`
                if (title) url += `&title=${title}`
                if (!closed_AD) url += '&show_ad=1'

                return jump(url, {
                    success: res => {
                        const data = {
                            integral,
                            seconds,
                            accuracy,
                            text: text_list[this._utils.randomNum(0, text_list.length - 1)].text
                        }

                        const json_set = this.rush_round_set?.tongue_twister
                         if (json_set) {
                             const {bg_img, blackboard_img, text_color, button, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (blackboard_img) data.blackboard_img = blackboard_img
                             if (text_color) data.text_color = text_color
                             if (button) data.button = button
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('gameData', data)
                    }
                })
            }

            if (types === 38) {
                const {title, per_integral, seconds = 30} = item
                let url = `/pages/games/penalty-kick/game?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&per_integral=${per_integral}&seconds=${seconds}`
                if (title) url += `&title=${title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const data = {}
                        
                        const json_set = this.rush_round_set?.penalty_kick
                         if (json_set) {
                             const {bg_img, goalkeeper_img, goal_img, football_img, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (goalkeeper_img) data.goalkeeper_img = goalkeeper_img
                             if (goal_img) data.goal_img = goal_img
                             if (football_img) data.football_img = football_img
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 39) {
                const {title, per_integral, seconds = 30} = item
                let url = `/pages/games/pony-run-fast/game?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&per_integral=${per_integral}&seconds=${seconds}`
                if (title) url += `&title=${title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const data = {}
                        
                        const json_set = this.rush_round_set?.pony_run_fast
                         if (json_set) {
                             const {bg_img, drum_img, pony_img, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (drum_img) data.drum_img = drum_img
                             if (pony_img) data.pony_img = pony_img
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 40) {
                const {title, per_integral, seconds = 30} = item
                let url = `/pages/games/eliminate-joy/game?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&per_integral=${per_integral}&seconds=${seconds}`
                if (title) url += `&title=${title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const data = {}
                        
                        const json_set = this.rush_round_set?.eliminate_joy
                         if (json_set) {
                             const {bg_img, star_imgs, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (star_imgs?.length) data.star_imgs = star_imgs
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 41) {
                const {title, per_integral, seconds = 30} = item
                let url = `/pages/games/balloon/game?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&per_integral=${per_integral}&seconds=${seconds}`
                if (title) url += `&title=${title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const data = {}

                        const json_set = this.rush_round_set?.balloon
                         if (json_set) {
                             const {bg_img, ball_img, boom_img, navigation_bar, boom_count} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (ball_img) data.ball_img = ball_img
                             if (boom_img) data.boom_img = boom_img
                             if (boom_count) data.boom_count = boom_count
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 42) {
                const {title, per_integral, seconds = 30} = item
                let url = `/pages/games/jump-upwards/game?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&per_integral=${per_integral}&seconds=${seconds}`
                if (title) url += `&title=${title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const data = {}

                        const json_set = this.rush_round_set?.jump_upwards
                         if (json_set) {
                             const {bg_img, doodler_img, platform_img, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (doodler_img) data.doodler_img = doodler_img
                             if (platform_img) data.platform_img = platform_img
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 43) {
                const {title, per_integral, seconds = 30} = item
                let url = `/pages/games/carrying-bricks/game?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&per_integral=${per_integral}&seconds=${seconds}`
                if (title) url += `&title=${title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const data = {}

                        const json_set = this.rush_round_set?.carrying_bricks
                         if (json_set) {
                             const {bg_img, pepole_img, left_btn_img, right_btn_img, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (pepole_img) data.pepole_img = pepole_img
                             if (left_btn_img) data.left_btn_img = left_btn_img
                             if (right_btn_img) data.right_btn_img = right_btn_img
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }

            if (types === 46) {
                const {title, per_integral} = item
                let url = `/pages/games/roll-dice/game?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&per_integral=${per_integral}`
                if (title) url += `&title=${title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const boBingSet = this.detail.conf.active.bo_bing || {}
                        res.eventChannel.emit('data', boBingSet)
                    }
                })
            }

            if (types === 47) {
                const {title, per_integral, seconds = 30} = item
                let url = `/pages/games/tangyuan-catcher/game?active_id=${this.id}&point_id=${point_id}&unit=${this.integral_unit}&per_integral=${per_integral}&seconds=${seconds}`
                if (title) url += `&title=${title}`
                if (!closed_AD) url += '&show_ad=1'
                return jump(url, {
                    success: res => {
                        const data = {}

                        const json_set = this.rush_round_set?.tang_yuan
                         if (json_set) {
                             const {bg_img, navigation_bar} = json_set
                             if (bg_img) data.bg_img = bg_img
                             if (navigation_bar) {
                                 const {bg_color = '#ffffff', font_color = '#000000'} = navigation_bar
                                 data.navigation_bar = {backgroundColor: bg_color, frontColor: font_color}
                             }
                         }

                        res.eventChannel.emit('data', data)
                    }
                })
            }
        },


        async toCompleteSportMomentTask(point_id, types, jump) {
            // 运动圈接口，类型 types   6   是运动圈    如果是发布动态圈的 则改为   17  动态圈（第二个运动圈功能）
            const sport_monent_type = types === 45 ? 17 : 6
            
            const jump2task = () => {
                jump(`/pages/comment/publish?active_id=${this.id}&point_id=${point_id}&sport_moment_types=${sport_monent_type}`)
            }

            const {count, start_time, end_time} = this.detail.conf.active.sport_moment_submit_limit || {}

            if (!count) {
                jump2task()
                return
            }

            if (start_time || end_time) {
                const now = new Date().getTime()
                const start = new Date(start_time.replace(/-/g, '/')).getTime()
                if (now < start) return this.$uni.showToast('任务未开始')
                const end = new Date(end_time.replace(/-/g, '/')).getTime()
                if (now > end) return this.$uni.showToast('任务已结束')
            }

            const userSubmitCount = await this.getUserSportMomentSubmitCount(sport_monent_type)
            if (userSubmitCount >= count) {
                return this.$uni.showToast(`已达任务次数上限${count}次，无法继续完成任务`, 'none', 3000)
            }

            jump2task()
        },

        async getUserSportMomentSubmitCount(types) {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.user.reply/reply_view_list',
                data: {
                    types,
                    pid: this.id,
                    is_long_id: 1,
                    only_myself: 1,
                    page: 1,
                    perpage: 1
                }
            })
            uni.hideLoading()

            return res?.data?.view_list?.total || 0
        },


        async toBodyDataSubmitTask(item) {
            if (this.detail.conf.active.admin_submit_body_data) {
                this.$uni.showToast('该任务数据由活动管理员统一录入', 'none', 5000)
                return
            }

            let weightRecord = false, waistRecord = false, bodyFatRecord = false

            const submitCount = item.finished_details?.finished_details || 0

            if (item.types === 'body') {
                item.type_list.forEach(types => {
                    if (types === 27) weightRecord = true
                    if (types === 28) bodyFatRecord = true
                    if (types === 29) waistRecord = true
                })
            } else {
                this.task_list.forEach(task => {
                    if (task.types === 27) weightRecord = true
                    if (task.types === 28) bodyFatRecord = true
                    if (task.types === 29) waistRecord = true
                })
            }




            if (submitCount === 1) {
                const res = await this.$uni.showModal('本次录入提交后将会确定最后数据，并不可修改，确定录入并提交?', {
                    showCancel: true
                })
                if (!res.confirm) return
            }

            this.$refs.bodyDataSubmit.open({
                weightRecord,
                bodyFatRecord,
                waistRecord,
                submitBirthday: submitCount === 0
            })
        },

        async getWeekStep() {
            this.$uni.showLoading('查询本周步数...')
            const res = await this.xwy_api.getWeRunData()
            uni.hideLoading()

            const stepList = res?.data?.['crypt_data']?.['stepInfoList'] || []

            if (!stepList.length) return 0


            const today = this._utils.getDay(0, true, '/')
            const weekDate = this._utils.getWeekDate(today, true, '/').map(item => new Date(item).getTime() / 1000)

            return stepList.reduce((sum, item) => {
                return weekDate.includes(item.timestamp) ? (sum + item.step) : sum
            }, 0)
        },

        async weekStepTaskSubmit() {
            this.$uni.showLoading()
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.job_list.puzzle/reward_user_integral',
                data: {
                    active_id: this.id,
                    sign: this._utils.randomCoding() + this._utils.base64['encode'](JSON.stringify({
                        types: 32,
                        point_id: this.current_level_id,
                        result: 'success'
                    }))
                }
            })
            uni.hideLoading()

            const result = {code: 1, info: '任务完成'}

            if (res?.status !== 1) {
                result.code = 0
                result.info = res?.info || '任务失败'
            }

            return result
        },

        async toCompleteWeekStepTask(stepNum) {
            // 本周步数查询比对
            const weekStep = await this.getWeekStep()
            if (weekStep < stepNum) return this.$uni.showToast(`本周步数为${weekStep}步,不足${stepNum}步`)

            // 任务提交
            const result = await this.weekStepTaskSubmit()
            if (result.code === 0) return this.$uni.showModal(result.info)
            this.$uni.showToast(result.info, 'success')
            this.successCheck(this.current_level_id)
        },

        successCheck(point_id) {
            this.taskListShow(point_id, true)
            setTimeout(() => {
                this.reLoadData()
            }, 1000)
        },

        bodyDataSubmitSuccess() {
            this.successCheck(this.current_level_id)
        },

        toCompleteStepTask(item) {
            // 下面代码为命名空间，为了解决webStorm警告，可忽略或删除
            /** @namespace item.finished_details.today_have_exchange */
            
            const task_type = this.detail.conf.active.job_set?.types || 1
            const max_step = item.max || 0
            const today_finished_step = item.finished_details?.today_have_exchange || 0
            if (task_type === 2 && max_step && today_finished_step && today_finished_step >= max_step) {
                return this.$uni.showToast('任务已完成')
            }

            const {open, exam} = this.detail.conf.active.exchange_step_answer || {}
            if (open && exam?.id) {
                return this.$uni.navigateTo(`/pages/likou_dati/pages/answer/answer_question/answer_question?exam_id=${exam.id}&activityID=${this.id}&point_id=${this.task_list_pointid}&task_activity_step_answer=1`, {
                    events: {
                        success: score => {
                            this.stepTaskAnswerEnd = true
                            this.stepTaskAnswerScore = score
                            this.stepTaskitem = item
                        }
                    }
                })
            }
            
            this.stepExchange(item)
        },

        stepTaskAnswerScoreCheck() {
            if (!this.stepTaskAnswerEnd) return
            this.stepTaskAnswerEnd = false

            let minScore = Number(this.detail.conf.active.exchange_step_answer?.min_score)
            if (!minScore || isNaN(minScore)) minScore = 0

            if (this.stepTaskAnswerScore < minScore) {
                return this.$uni.showToast(`需要达到${minScore}分才能完成任务`, 'none', 3000)
            }

            this.stepExchange(this.stepTaskitem)
        },

        stepExchange(item) {
            this.$uni.showLoading('获取步数中...')
            xwy_api.getWeRunData(res => {
                uni.hideLoading()
                if (res?.data?.['crypt_data']?.['stepInfoList']?.length) {
                    const step_list = res.data['crypt_data']['stepInfoList']
                    const today_step = step_list[step_list.length - 1].step || 0
                    this.stepExchangeAjax(item, today_step)
                } else {
                    this.$uni.showToast('获取步数失败', 'error')
                }
            })
        },

        async stepExchangeAjax(item, step) {
            const clearance_type = this.detail.conf.active.job_set?.types || 1

            if (clearance_type === 1) {
                const need_step = item.min
                if (step < need_step) return this.$uni.showToast(`今日步数不足${need_step}步`)
            }

            const urls = {
                1: 'front.flat.sport_step.job_list.jobState/exchange_step_for_rush_round',
                2: 'front.flat.sport_step.exchange/exchange_step'
            }
            const data = {active_id: this.id}
            if (clearance_type === 1) data.point_id = this.task_list_pointid
            if (clearance_type === 2) {
                data.exchange_date = Math.floor(new Date(utils.getDay(0, true, '/')).getTime() / 1000)
                data.point_id = this.task_list_pointid
            }


            this.$uni.showLoading('提交步数...')
            const res = await xwy_api.request({url: urls[clearance_type], data})
            uni.hideLoading()
            if (res?.status !== 1) return this.$uni.showModal(res?.info || '步数提交失败')
            const {add_step = 0, add_integral} = res?.data?.exchange_result || {}
            this.$uni.showToast(`本次提交步数${add_step}步，增加${add_integral}${this.integral_unit}`, 'none', 3000)
            await this.taskListShow(this.task_list_pointid, true)
            await this.reLoadData()
        },


        iconItemClick(name) {
            const options = {
                'set': () => this.toManage(),
                'ranking_list': () => this.toTopList(),
                'user_info': () => this.uniPopupOpen('my_info'),
                'exam': () => this.toExamList(),
                'sport_comment': () => this.toSportMomentList(),
                'active_rule': () => this.uniPopupOpen('activity_detail'),
                'shopping': () => this.toShop(),
                'article_reading': () => this.toNewsList(this.detail.conf.active.article_reading.category_id),
                'active_notice': () => this.showNoticePopup(true),
                'share': () => this.uniPopupOpen('sharePopup'),
                'lottery': () => this.toLottery(),
                'certificate': () => this.lookCertificate(),
            }

            options[name]?.()
        },
        
        toManage() {
            this.$uni.navigateTo(`../admin/manage?id=${this.id}`)
        },

        toTopList() {
            uni.navigateTo({
                url: './ranking-list?id=' + this.id
            })
        },

        toExamList() {
            this.$uni.navigateTo(`/pages/activity/other/answer/list?id=${this.id}&compare_types=1`)
        },

        lookWeekTaskNews(types) {
            const {types_31_news, types_33_news, types_34_news} = this.detail.conf.active
            if (types === 31 && types_31_news?.news_id) {
                this.$uni.navigateTo(`/pages/news/preview?id=${types_31_news.news_id}`)
                return
            }
            if (types === 33 && types_33_news?.news_id) {
                this.$uni.navigateTo(`/pages/news/preview?id=${types_33_news.news_id}`)
                return
            }
            if (types === 34 && types_34_news?.news_id) {
                this.$uni.navigateTo(`/pages/news/preview?id=${types_34_news.news_id}`)
            }
        },

        toSportMomentList(types) {
            // 运动圈任务别名可以在活动设置里修改，动态圈暂时没有，就叫动态圈。
            // 运动圈接口，类型 types   6   是运动圈    如果是发布动态圈的 则改为   17  动态圈（第二个运动圈功能）
            let sport_moment_name = this.sportMomentName,
                sport_moment_types = 6
            if (types === 45) {
                sport_moment_name = '动态圈'
                sport_moment_types=  17
            }

            this.$uni.navigateTo(`/pages/comment/list?active_id=${this.id}&not_publish=1&sport_moment_name=${sport_moment_name}&sport_moment_types=${sport_moment_types}`)
        },

        toShop() {
            app.globalData['tempData'].shop_integral_unit = this.integral_unit
            app.globalData['tempData'].shop_exchange_limit = this.rank_set.exchange_gift_once || 0
            let url = '/pages/shop/goods/list?active_id=' + this.id
            uni.navigateTo({
                url
            })
        },

        lookMedal3() {
            if (!this.detail.conf.active.reward_medal?.list?.length) {
                this.$uni.showToast('活动未设置勋章')
                return
            }

            const username = this.must_submit?.[0]?.value || ''
            const headimg = this.headimg || ''

            this.$uni.navigateTo(`./medal3-list?active_id=${this.id}&username=${username}&headimg=${headimg}`)
        },

        toActiveReport() {
            this.$uni.navigateTo(`/pages/other/user-activity-report/report?active_id=${this.id}`)
        },

        toLottery() {
            const task_lottery_list = this.detail.conf.active['task_lottery_list']
            if (!task_lottery_list?.length) return this.$uni.showToast('活动未绑定抽奖')

            this.$uni.navigateTo(`/pages/lottery/user/lottery?type=just_lottery&id=${task_lottery_list[0].lottery_id}&active_id=${this.id}&point_id=${this.current_level_id}`)
        },

        toSign() {
            this.$uni.navigateTo(`/pages/clock_in/user/point_list?id=${this.id}&point_types=1`)
        },

        toSignSquare() {
            this.$uni.navigateTo(`/pages/clock_in/user/public_sign_list?id=${this.id}&types=5`)
        },

        toNewsList(id) {
            this.$uni.navigateTo(`/pages/news/list?category_id=${id}`)  
        },

        changeDetailContent(news_id) {
            xwy_api.ajax({
                url: "front.news/news_details",
                data: {
                    news_id,
                    access_token: app.globalData['access_token']
                },
                success: res => {
                    uni.hideLoading()

                    uni.hideLoading()
                    if (!res.data || !res.data['news_details']) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '文章内容获取失败',
                            showCancel: false,
                            success: () => uni.navigateBack()
                        })
                        return false
                    }

                    const detail = res.data['news_details']


                    if (detail.video_url) {
                        let video_type = 'txv_id'
                        if (detail.video_url.startsWith('http://') || detail.video_url.startsWith('https://')) {
                            video_type = 'http'
                        }
                        detail.video_type = video_type
                    }


                    if (detail.content) {
                        detail.content = utils.newsContentInit(detail.content)
                    }

                    this.news_detail = detail
                }
            })
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    setTimeout(() => this.$refs.input_password.open(), 30)
                }

                // 体验版不需要输入密码
                if (evn_version === 'trial') {
                    return uni.showModal({
                        title: '提示',
                        content: '此活动设置了活动密码，请勿报名参与活动！！！',
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => {
                            res.confirm && passwordDialogShow()
                        }
                    })
                }


                passwordDialogShow()
            }

            this.showNoticePopup()
        },

        passwordInputConfirm() {
            const password = this.password
            if (!password) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.rank_set.shield_other) {
                    return uni.showModal({
                        title: '提示',
                        content: '请输入密码',
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res.cancel) {
                                uni.navigateTo({
                                    url: '/pages/user/user'
                                })
                            }
                        }
                    })
                }


                return this.$uni.showToast('请输入密码')
            }
            this.checkActivityPassword()
        },

        async checkActivityPassword() {
            this.$uni.showLoading('密码验证中...')

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    active_id: this.id,
                    password: this.password
                }
            })
            uni.hideLoading()

            if (res?.status === 1) {
                my_storage.rememberActivityPassword(this.id, this.password)
                this.$refs.input_password.close()

                this.$uni.showToast('密码正确', 'success')
                await this.showNoticePopup()
                return
            }

            await this.$uni.showModal(res?.info || '密码错误')
        },

        passwordInputClose() {

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版并且没有上一 页面，重新弹出输入密码窗口
            if (app.globalData['shop_info']?.extend_set?.shield_other_active?.active_id)
                return this.$uni.showToast('请输入活动密码')

            this.$uni.reLaunch('/pages/index/index')
        },


        async updateHeadimg() {
            uni.navigateTo({
                url: `/pages/other/headimg-list/headimg-list?active_id=${this.id}`,
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        this.$uni.showLoading('修改中...')
                        if (!obj.temp) return this.$refs.mustSubmitPopup.uploadHeadimg(obj.src)
                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await xwy_api.uploadOneImage(data)
                        this.$refs.mustSubmitPopup.uploadHeadimg(headimg)
                    }
                }
            })
        },

        joinActivity() {
            if (this.loading) return
            if (this.detail.conf?.active?.submit) {
                const now_time = new Date().getTime()
                const {begin, end} = this.detail.conf.active.submit
                if (begin) {
                    const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                    if (now_time < begin_time) return this.$uni.showToast(`活动于${begin}开始报名`)
                }
                if (end) {
                    const end_time = new Date(end.replace(/-/g, '/')).getTime()
                    if (now_time > end_time) return this.$uni.showToast(`活动于${end}截止报名`)
                }
            }

            this.uniPopupOpen('mustSubmitPopup')
        },

        updateAttendDetailShow() {
            this.update_attend_details = true
            this.uniPopupOpen('mustSubmitPopup')
        },

        mustSubmitPopupClose() {
            this.uniPopupClose('mustSubmitPopup')
        },

        mustSubmitUpdate() {
            this.uniPopupClose('mustSubmitPopup')
            this.init()
        },
        

        lookIntegralRecord() {
            let url = `/pages/sign_in/integral_record_list?active_id=${this.id}&unit=${this.integral_unit}`

            // OA开启了显示当日积分
            if (this.rank_set['rushing_round_index_current_day_integral']) url += '&show_one_day=1'

            this.$uni.navigateTo(url)
        },



        /**
         * @description 打开阅读须知弹窗
         * @param {Boolean} isClick   用来区分是自动弹出还是手动点击弹出，手动点击就算已报名也要弹出
         * */
        async showNoticePopup(isClick = false) {
            const {active_details_notice_list, is_joining} = this

            // 没有配置阅读须知文章，一定不能弹
            if (!active_details_notice_list.length) return
            
            // 没有报名要弹，报名以后不自动弹，但是手动点击右侧图标需要弹
            if (!is_joining || isClick) this.uniPopupOpen('notice_popup')
        },
        
        

        copy(data, hide_toast = false) {
            uni.setClipboardData({
                data,
                success: () => hide_toast ? uni.hideToast() : this.$uni.showToast('复制成功', 'none', 500)
            })
        },

        uniPopupClose(ref) {
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.$refs[ref].open()
        }
    }
}
</script>

<style lang="scss">

.join-btn {
    width: 250px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
}

$popup-title-bgi: "https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/flat/sport_step/task_challenge/style1/sty2.png";

.task-list-popup, .join-popup-c, .uni-popup-info, .share-popup, .password-popup, .uni-popup-- {
    position: relative;
    background-color: #FFD67A;
    border-radius: 10px;
    padding: 10px;
    box-sizing: border-box;

    .popup-container {
        background-color: #fff;
        border-radius: 10px;
        padding: 10px;
    }

    .task-list-popup-title,
    .join-popup-c-title,
    .uni-popup-info-title,
    .share-popup-title,
    .password-popup-title,
    .uni-popup--title {
        position: absolute;
        top: -22px;
        left: 50%;
        margin-left: -110px;
        width: 220px;
        height: 44px;
        line-height: 44px;
        text-align: center;
        color: #fff;
        background-image: url(#{$popup-title-bgi});
        background-size: 100% 100%;
        background-repeat: no-repeat;
    }

    .join-popup-buttons, .password-popup-buttons {
        display: flex;
        justify-content: center;
        align-items: center;
        padding-bottom: 10px;

        .join-popup-cancel-button, .join-popup-confirm-button,
        .password-popup-confirm-button, .password-popup-cancel-button {
            width: 100px;
            line-height: 40px;
            border-radius: 20px;
            text-align: center;
            margin: 10px;
            font-size: 18px;
        }
    }

    .join-popup-confirm-button, .password-popup-confirm-button {
        background-color: #ff985e;
        color: #fff;
    }

    .join-popup-cancel-button, .password-popup-cancel-button {
        background-color: #f8dda0;
        color: #f1f1f1;
    }
}

.uni-popup-info {
    width: 90vw;

    .headimg {
        width: 80px;
        height: 80px;
        border-radius: 50%;
    }

    .user-info-item {
        margin-bottom: 10px;
        padding: 10px;
        border: 1px solid #feedcd;
        background-color: #fffdfa;
        border-radius: 5px;
    }
}

.detail-popup {
    width: 95vw;
    box-sizing: border-box;

    .detail-popup-detail {
        max-height: calc(100vh - 300px);
        box-sizing: border-box;
        margin-top: 10px;
    }
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}

.current-level-image-container {
    position: relative;

    .current-level-image {
        width: 100vw;
        height: auto;
        display: block;
    }

    .image-diy-task {
        position: absolute;
    }
}


.action-button {
    width: 150px;
    height: 36px;
    line-height: 36px;
    border-radius: 18px;
    background-color: #f3bb3a;
    color: #fff;
    text-align: center;
    margin: 0 auto;
}

.action-button-fixed {
    position: fixed;
    z-index: 9;
    bottom: 30px;
    left: 50%;
    margin-left: -100px;
    width: 200px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
    font-size: 18px;
}

.task-list-popup {
    .task-tips {
        margin-top: -5px;
    }

    .task-list {
        overflow-y: auto;
        margin: 10px 0;
        max-height: calc(90vh - 150px);
    }
}

.password-popup {
    width: 80vw;
    padding: 0 30px;
    box-sizing: border-box;

    .password-popup-buttons {
        padding-top: 20px;
    }
}

.uni-popup-- {
    width: 80vw;
}

.top-msg {
    position: fixed;
    top: 10px;
    display: flex;
    flex-direction: row;

    .top-msg-icon {
        position: absolute;
        left: -15px;
        top: -3px;
        border-radius: 50%;
        padding: 3px;
        .top-msg-inner-ring {
            width: 24px;
            height: 24px;
            line-height: 24px;
            text-align: center;
            border-radius: 50%;
            .iconfont {
                color: #fff;
            }
        }
    }

    .top-msg-num {
        background-color: rgba(0, 0, 0, .3);
        color: #fff;
        height: 24px;
        line-height: 24px;
        border-radius: 0 12px 12px 0;
        padding: 0 12px 0 20px;
        min-width: 60px;
        text-align: center;
        font-size: 14px;

        &.need-show-text {
            min-width: 100px;
        }
    }
}
.top-msg-left {
    left: calc(3vw + 15px);

    .top-msg-icon {
        background-color: #5cadff;
        box-shadow: 0 0 5px #2d8cf0;
        .top-msg-inner-ring {
            background-color: #2d8cf0;
        }
    }
    .top-msg-num {
        box-shadow: 0 0 10px rgba(45, 140, 240, .2) inset;
    }
}
.top-msg-right {
    right: 3vw;

    .top-msg-icon {
        background-color: #fce230;
        box-shadow: 0 0 5px #ffb400;
        .top-msg-inner-ring {
            background-color: #ffb400;
        }
    }
    .top-msg-num {
        box-shadow: 0 0 10px rgba(255, 180, 0, .2) inset;
    }
}

.top-msg-medal {
    top: 50px;
}

.bg-music {
    position: fixed;
    top: 50px;
    right: 3vw;
    width: 40px;
    height: 40px;
    z-index: 9;
}
</style>
