<template>
    <view class="page bg-background">

        <screen-picture
            v-if="screen_pic_show"
            :hide="screen_pic_hide"
            :image-src="screen_pic"
            :time="screen_pic_count_down"
            :show-button="true"
            @skipScreen="skipScreen"
        />


        <activity-delete-tips v-if="error" :activeId="id"/>

        <view v-if="detail && detail.active_id">
            <template v-if="detail.rank_set && detail.rank_set['barrage']">
                <lff-barrage ref="lffBarrage" :activeid="id"></lff-barrage>
            </template>

            <view
                v-if="detail.conf && detail.conf.active && detail.conf.active.audio_src"
                class="audio flex-all-center"
                :class="{'audio_rotate': audio_play}" @click="audioControl"
            >
                <text class="iconfont icon-background-music-play font24 color-white"></text>
            </view>


            <activity-logo-title-time :details="detail"/>


            <view class="icon-list flex-row flex-wrap text-center bdb-10 bg-white">

                <navigator v-if="is_my_activity" class="icon-item" :url="'../admin/manage?id=' + id">
                    <text class="iconfont font24 color-primary icon-setting"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动管理</view>
                </navigator>
                <view v-if="!is_joining" class="icon-item" @click="joinActivity">
                    <text class="iconfont font24 color-primary icon-users"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">参与活动</view>
                </view>
                <view v-if="is_joining" class="icon-item" @click="uniPopupOpen('my_info')">
                    <text class="iconfont font24 color-primary icon-personal-data"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">报名信息</view>
                </view>


                <view v-if="!detail.conf.active.activity_rules_hide || detail.content || news_detail"
                      class="icon-item" @click="uniPopupOpen('activity_detail')">
                    <text class="iconfont font24 color-primary icon-feedback"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">活动说明</view>
                </view>

                <view class="icon-item" hover-class="navigator-hover" @click="toRecord(false)">
                    <text class="iconfont font24 color-primary icon-wechat-movement"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">运动广场</view>
                </view>

                <view v-if="!detail.rank_set || !detail.rank_set['closed_top_rank']" class="icon-item"
                      hover-class="navigator-hover" @click="toTopList()">
                    <text class="iconfont font24 color-primary icon-trophy"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">排行榜</view>
                </view>

                <view class="icon-item" hover-class="navigator-hover" @click="toRecord(true)">
                    <text class="iconfont font24 color-primary icon-walk"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">我的记录</view>
                </view>


                <view v-if="detail.rank_set && detail.rank_set['diy_books']" class="icon-item"
                      hover-class="navigator-hover" @click="lookCertificate">
                    <text class="iconfont font24 color-primary icon-certificate"></text>
                    <view class="color-sub font14 pt3">我的证书</view>
                </view>


                <navigator
                    v-if="detail.rank_set && detail.rank_set['open_sport_moment']"
                    class="icon-item"
                    :url="'/pages/comment/list?active_id=' + id"
                >
                    <text class="iconfont font24 color-primary icon-wechat-movement"></text>
                    <view class="color-sub font14 pt3">运动圈</view>
                </navigator>


                <navigator
                    v-if="detail.rank_set && detail.rank_set['face_swap'] && checked === 1"
                    class="icon-item"
                    :url="'/pages/other/ai-face?active_id=' + id"
                >
                    <uni-icons type="images" size="24" color="#2d8cf0"/>
                    <view class="color-sub font14 pt3">AI换脸</view>
                </navigator>

                <template v-if="headimg_plugin && headimg_plugin.length">
                    <view v-for="(item, index) in headimg_plugin" :key="index" class="icon-item"
                          @click="toIdeaAvatar(item)">

                        <image
                            v-if="item.key === 'chajian'"
                            :src="item.icon || 'https://apissl.xinweiyun.com/weixin/public/img/longmarch/icon_guajian.png'"
                            style="width: 24px; height: 24px;"
                        />

                        <image
                            v-if="item.key === 'beijing'"
                            :src="item.icon || 'https://apissl.xinweiyun.com/weixin/public/img/longmarch/icon_beijing.png'"
                            style="width: 24px; height: 24px;"
                        />
                        <view class="color-sub font14" style="padding-top: 1px;">{{ item.name }}</view>
                    </view>
                </template>


                <view
                    v-if="detail.rank_set && detail.rank_set['barrage']"
                    class="icon-item"
                    @click="toBulletScreen()"
                >
                    <uni-icons type="chatboxes" size="24" color="#2d8cf0"/>
                    <!-- <text class="iconfont font24 color-primary icon-empty-state"></text> -->
                    <view class="color-sub font14" style="padding-top: 3px;">活动弹幕</view>
                </view>


                <template v-if="detail.conf.active.detail_icon_list">

                    <navigator
                        v-for="(item, index) in detail.conf.active.detail_icon_list"
                        :key="index"
                        class="icon-item"
                        :url="item.type === 0 ? ('/pages/news/preview?id=' + item.id) : ('/pages/news/list?category_id=' + item.id)"
                    >
                        <text :class="'iconfont font24 color-primary ' + item.icon"></text>
                        <view class="color-sub font14" style="padding-top: 3px;">{{ item.title }}</view>
                    </navigator>
                </template>


                <navigator
                    v-if="!detail.conf.active.close_feedback"
                    class="icon-item"
                    :url="'/pages/activity/feedback/send?activity_id=' + id"
                >
                    <text class="iconfont font24 color-primary icon-chat-bubble"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">意见反馈</view>
                </navigator>

                <view class="icon-item" @click="showActiveSharePopup">
                    <text class="iconfont font24 color-primary icon-share"></text>
                    <view class="color-sub font14" style="padding-top: 3px;">分享</view>
                </view>
            </view>


            <view class="p10 bdb-10 bg-white">
                <view v-if="detail.conf.active.enter_types === 3 && is_joining && checked === 0"
                      class="join-container">
                    <view class="pb5 color-sub text-center font14">需要管理员审核通过后才能去运动</view>
                    <view class="flex-all-center">
                        <view class="join-btn bg-background color-sub">去运动</view>
                    </view>
                </view>

                <view v-else class="join-container">
                    <view class="flex-all-center">
                        <view v-if="is_joining" class="join-btn bg-primary color-white" @click="toRun">
                            去运动
                        </view>
                        <view v-else class="join-btn bg-primary color-white" @click="joinActivity">
                            参与活动
                        </view>
                    </view>
                </view>
            </view>


            <xwy-ad
                v-if="!password_dialog_show && !join_popup_show && !popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)"
                :ad_type="4"
            ></xwy-ad>

            <view v-if="technology_support" class="flex-all-center" style="padding-top: 30px;">

                <navigator
                    v-if="technology_support.news_id"
                    :url="'/pages/news/preview?id=' + technology_support.news_id + '&type=support'"
                    class="text-center font14 color-sub p10"
                >{{ technology_support.button_text }}
                </navigator>
                <view v-else class="text-center font14 color-sub p10">
                    {{ technology_support.button_text }}
                </view>
            </view>

            <view v-if="!detail.rank_set || !detail.rank_set['closed_user_center']" class="flex-all-center">
                <navigator url="/pages/user/user" class="p10 color-sub font14">个人中心</navigator>
            </view>

            <xwy-ad v-if="(!detail.rank_set || !detail.rank_set.closed_AD) && !screen_pic_show"
                    :ad_type="3"></xwy-ad>

            <iup ref="input_username_password" :labels="detail.conf.active.batch_import_label"/>

        </view>

        <view v-if="join_popup_show" class="join-popup flex-all-center bg-white" @touchmove.stop.prevent="">
            <view>
                <view class="join-popup-c bg-white">
                    <view class="text-center font18 color-content p10">
                        <template v-if="update_attend_details">修改报名信息</template>
                        <template v-else>参加活动</template>
                    </view>


                    <template v-if="must_submit.length">
                        <template v-for="(item, index) in must_submit">

                            <input
                                v-if="item.types === 1 || item.types === 3"
                                :key="index"
                                class="join-input"
                                :value="item.value"
                                @blur="mustTextValueChange($event, index)"
                                :placeholder="'请输入' + item.title + (item.rules === 1 ? ' (必填)' : '')"
                            />
                            <picker
                                v-if="item.types === 2"
                                :range="item.options"
                                range-key="text"
                                @change="mustValueChange($event, index)"
                            >
                                <view class="join-input flex-kai">
                                    <view v-if="!item.value" class="color-sub">
                                        请选择{{ item.title }}{{ item.rules === 1 ? ' (必选)' : '' }}
                                    </view>
                                    <view v-if="item.value">{{ item.value }}</view>
                                    <text class="iconfont icon-more color-disabled font18"/>
                                </view>
                            </picker>
                        </template>

                    </template>



                    <picker
                        v-if="detail.rank_set && detail.rank_set['select_sex']"
                        :range="sexPicker"
                        :value="sex === 2 ? 1 : 0"
                        range-key="title"
                        @change="sex = Number($event.detail.value) === 0 ? 1 : 2"
                    >
                        <view class="join-input flex-kai">
                            <view v-if="!sex" class="color-sub">
                                请选择性别{{ detail.conf.active.sex_required ? ' (必选)' : '' }}
                            </view>
                            <view v-if="sex">{{ sex === 1 ? sexPicker[0].title : sexPicker[1].title }}</view>
                            <text class="iconfont icon-more color-disabled font18"/>
                        </view>
                    </picker>

                    <view v-if="detail.rank_set && detail.rank_set.team_group_open && !update_attend_details"
                          class="join-input flex-kai" @click="changeTeam">
                        <template>
                            <view v-if="team_id">{{ team_name || team_id }}</view>
                            <view v-else class="color-sub">
                                <text>请选择队伍</text>
                                <text v-if="detail.conf.active.team_required" class="pl5">(必选)</text>
                            </view>
                        </template>
                        <text class="iconfont icon-more color-disabled font18"/>
                    </view>


                    <view class="join-popup-btns flex-row text-center font18">
                        <view class="join-popup-btn-cancel color-sub" @click="cancelJoin">取消</view>
                        <view class="join-popup-btn-confirm color-success" @click="joinAjax">确定</view>
                    </view>
                </view>

                <template v-if="!detail.rank_set || !detail.rank_set.closed_AD">
                    <view class="pt5">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>


        </view>

        <active-share ref="activeShare"/>

        <uni-popup ref="activity_detail" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active"
                  class="uni-popup-info detail-popup bg-white">
                <view class="popup-close" @click="uniPopupClose('activity_detail')">
                    <uni-icons type="close" size="28" color="#b2b3b7"/>
                </view>

                <scroll-view scroll-y="true" class="detail-popup-detail"
                             style="max-height: calc(100vh - 200px); padding: 10px 0;">
                    
                    <view v-if="!detail.conf.active.activity_rules_hide" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动规则 -</view>

                        <view class="color-content font16">
                            活动参与方式：
                            <template v-if="detail.conf.active.enter_types === 1">
                                自由报名参与活动
                            </template>
                            <template v-if="detail.conf.active.enter_types === 2">
                                需要输入密码才能报名
                            </template>
                            <template v-if="detail.conf.active.enter_types === 3">
                                报名需要审核通过才能参与活动
                            </template>
                        </view>


                    </view>

                    <view v-if="detail.content || news_detail" class="p10 bdb-10 bg-white">
                        <view class="text-center color-sub pb5">- 活动说明 -</view>
                        <view class="color-content font16">
                            <template v-if="detail.content && !news_detail">
                                <rich-text :nodes="detail.content" space="nbsp"></rich-text>
                            </template>
                            <template v-if="news_detail && news_detail.content">
                                <u-parse :content="news_detail.content"/>
                            </template>
                        </view>
                    </view>
                    <xwy-ad v-if="!loading && (!detail.rank_set || !detail.rank_set.closed_AD)"
                            :ad_type="66"></xwy-ad>
                </scroll-view>
            </view>
        </uni-popup>

        <uni-popup ref="my_info" type="center" @touchmove.stop.prevent="">
            <view v-if="detail && detail.conf && detail.conf.active" class="uni-popup-info bg-white p20">
                <view class="popup-close" @click="uniPopupClose('my_info')">
                    <uni-icons type="closeempty" size="24" color="#b2b3b7"/>
                </view>
                <view class="text-center color-sub pb5">- 活动报名信息 -</view>
                <view class="text-center p10">

                    <image
                        class="headimg"
                        :src="headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
                        mode="aspectFill"
                    />
                    <view>
                        <text class="color-primary" @click="updateHeadimg">更改头像</text>
                    </view>
                </view>


                <view
                    class="color-content font16 ptm5"
                    v-for="(item, index) in must_submit"
                    :key="index"
                    @click="updateAttendDetailShow"
                >
                    <text>
                        {{ item.title }}：
                        <template>
                            <template v-if="item.value">{{ item.value }}</template>
                            <template v-else>
                                <template v-if="item.types === 1">未填写</template>
                                <template v-if="item.types === 2">未选择</template>
                            </template>
                        </template>
                    </text>
                    <text v-if="is_joining" class="iconfont icon-edit color-sub pl5"></text>
                </view>

                <view v-if="detail.rank_set && detail.rank_set['select_sex']"
                      class="color-content font16 ptm5" @click="updateAttendDetailShow">
                    <text>性别：{{ sex ? sex === 1 ? sexPicker[0].title : sexPicker[1].title : '未选' }}</text>
                    <text v-if="is_joining" class="iconfont icon-edit color-sub pl5"></text>
                </view>

                <view v-if="detail.rank_set && detail.rank_set.team_group_open"
                      class="color-content font16 ptm5">
                    <text>
                        队伍：{{ user_details.team_details && user_details.team_details.name || '未加入队伍' }}
                    </text>
                    <template v-if="!user_details.team_id">
                        <text class="color-primary pl5" @click="updateAttendDetailShow">加入队伍</text>
                        <uni-icons type="forward" color="#2d8cf0" @click="updateAttendDetailShow"/>
                    </template>
                </view>


                <template v-if="popup_open && (!detail.rank_set || !detail.rank_set.closed_AD)">
                    <view style="position: relative; left: -10px;">
                        <xwy-ad :ad_type="66"></xwy-ad>
                    </view>
                    <xwy-ad :ad_type="3"></xwy-ad>
                </template>
            </view>
        </uni-popup>


        <template v-if="password_dialog_show">
            <uni-popup
                ref="input_password"
                type="dialog"
                mode="input"
                :is-mask-click="false"
                @maskClick="copy(id)"
            >
                <uni-popup-dialog
                    mode="input"
                    title="活动密码"
                    :value="password"
                    placeholder="请输入活动密码"
                    @confirm="passwordInputConfirm"
                    @close="passwordInputClose"
                ></uni-popup-dialog>
            </uni-popup>
        </template>

        <expiration-reminder ref="expirationReminder"/>


        <uni-popup ref="missSubmitPopup" :is-mask-click="false">
            <view class="miss-submit-popup bg-white radius10 p10">
                <view class="p10 text-center color-title">你有未提交的运动记录，是否提交？</view>
                <view class="flex-all-center p10">
                    <view class="p10 font18 color-content">
                        <view>距离: {{ missSubmitRunData.meter }}km</view>
                        <view>用时: {{ missSubmitRunData.seconds }}</view>
                        <view>配速: {{ missSubmitRunData.pace }}</view>
                        <view v-if="missSubmitRunData.calories">
                            消耗: {{ missSubmitRunData.calories }}千卡
                        </view>
                    </view>
                </view>
                <view v-if="openMotionRouteSetShow" class="flex-all-center p10">
                    <view @click="openMotionRoute = !openMotionRoute">

                        <radio class="radio" :checked="openMotionRoute"/>
                        <text class="color-sub font14">公开运动轨迹</text>
                    </view>
                </view>

                <view class="flex-all-center">
                    <view class="submit-button bg-primary color-white text-center"
                          hover-class="navigator-hover" @click="submitMissRunData">
                        提交运动记录
                    </view>
                </view>

                <view class="flex-all-center">
                    <view class="p10 color-sub font14" @click="removeMissSubmitRunData">删除记录</view>
                </view>
            </view>
        </uni-popup>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import my_storage from '@/utils/storage.js'
import bs_socket from '@/utils/bullet_screen_socket.js'
import activity_tool from '@/utils/acyivity_tool.js'
import runTimeCheck from '../run-time-check'

import lffBarrage from '@/components/lff-barrage.vue'
import inputUsernamePassword from '@/components/input-username-password.vue'

let interval
let innerAudioContext

export default {
    components: {
        iup: inputUsernamePassword,
        lffBarrage
    },
    data() {
        return {
            evn_version: app['globalData'].evn_version,
            show_tab: false,
            from_tab: false,
            loading: true,
            screen_pic: '',
            screen_pic_show: false,
            screen_pic_hide: false,
            screen_pic_count_down: null,
            id: '',
            userid: '',
            is_my_activity: false,
            detail: {},
            user_details: {},
            audio_play: false,
            error: '',
            join_popup_show: false,
            update_attend_details: false,
            headimg: '',
            username: '',
            checked: 0,
            is_joining: false,
            password: '',
            technology_support: false,
            must_submit: [],
            headimg_plugin: [],
            password_dialog_show: false,
            popup_open: false,
            news_detail: null,
            platform: uni.getSystemInfoSync().platform,
            sex: 0,

            team_id: '',
            team_name: '',

            // 提交意外退出的跑步记录时是否需要显示勾选是否公开跑步轨迹地图
            openMotionRouteSetShow: true,
            // 是否允许查看跑步轨迹地图
            openMotionRoute: true,
            // 未提交的运动记录数据
            missSubmitRunData: {meter: 0, seconds: 0, pace: 0},
        }
    },
    
    computed: {
        sexPicker() {
            let open = false, sex_label = {man: '男', woman: '女'}
            try {
                open = !!this.detail?.rank_set?.['select_sex']
                if (this.detail?.conf?.active?.sex_label) sex_label = this.detail.conf.active.sex_label
            } catch (e) {}

            if (!open) return []
            return open ? [{value: 1, title: sex_label.man}, {value: 2, title: sex_label.woman}] : []
        }
    },
    
    onLoad(e) {

        console.log('活动详情页面路径参数', e)


        let isH5 = false
        // #ifdef H5
        isH5 = true
        // #endif
        if (isH5) return this.$uni.showModal('请在小程序内打开', {success: () => uni.navigateBack()})

        if (e.from && e.from === 'tab') {
            this.from_tab = true
            this.$uni.hideHomeButton()
        }

        if (uni.getLaunchOptionsSync().scene === 1154) return this.getSimpleDetail(e.id)

        e.screen_pic ? this.screenPicShow(e.screen_pic) : this.$uni.showLoading('数据加载中...')

        login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err.errTitle || '提示'})
            }

            if (this.from_tab) this.show_tab = true

            if (e.scene) return this.analysisScene(e.scene)

            if (!e.id) {
                this.loading = false
                this.error = '请指定活动id'
                return uni.hideLoading()
            }


            this.id = e.id

            this.userid = app['globalData'].userid

            this.getDetail()
        })
    },
    onShow() {
        if (this.detail?.conf?.active?.audio_src) innerAudioContext.play()
    },
    onHide() {
        if (this.audio_play) innerAudioContext.pause()
    },
    onUnload() {
        if (this.detail?.conf?.active?.audio_src) innerAudioContext.destroy()
        if (bs_socket.socketTask) {
            this.unload = true
            bs_socket.socketTask.close()
        }
    },

    onShareAppMessage() {
        this._utils.shareCopyActivityId(this.detail)

        let url = '/pages/running/user/detail?id=' + this.id
        if (this.detail?.conf?.active?.screen_pic) url += `&screen_pic=${this.detail.conf.active.screen_pic}`
        if (this.from_tab) url += '&from=tab'

        return {
            title: this.detail?.conf?.active?.share_title || this.detail?.name || '',
            path: url,
            imageUrl: this.detail?.conf?.active?.share_image || this.detail?.logo || ''
        }
    },

    onShareTimeline() {
        return {
            title: this.detail.name,
            imageUrl: this.detail.logo || ''
        }
    },

    methods: {
        getNicknameHeadimg() {
            const userinfo = app['globalData'].userinfo || {}
            let nickname = userinfo.nickname || '匿名用户',
                headimg = userinfo.headimg || ''

            if (this.must_submit?.[0]?.value) nickname = this.must_submit[0].value
            if (this.headimg) headimg = this.headimg

            return {nickname, headimg}
        },

        toBulletScreen() {
            const {nickname, headimg} = this.getNicknameHeadimg()

            let url = `/pages/activity/other/bullet_screen?active_id=${this.id}&nickname=${nickname}&headimg=${headimg}`

            if (this.active_more_data?.['active_conf_set']?.['barrage']) {
                const barrage = this.active_more_data['active_conf_set']['barrage']
                if (barrage.background) url += `&bgi=${barrage.background}`
                if (barrage['submit_button_text']) url += `&send_text=${barrage['submit_button_text']}`
                if (barrage['navigation_bar']) {
                    url += `&navigation_bar=${JSON.stringify(barrage['navigation_bar'])}`
                }
            }
            this.$uni.navigateTo(url)
        },

        webSocketInit(reconnect = false) {
            bs_socket.webSocketInit(() => {
                bs_socket.socketTask.onMessage(res => {
                    console.log('【WEBSOCKET】收到消息', res.data)
                    this.receiveMessages(res.data)
                })
                bs_socket.socketTask.onOpen(res => {
                    console.log('【WEBSOCKET】', '链接成功！', res)
                    uni.hideLoading()
                    if (!reconnect) this.joinSocket()
                })
                bs_socket.socketTask.onClose(res => {
                    console.log('【WEBSOCKET】链接关闭！', res)
                    uni.hideLoading()
                    !this.unload && this.webSocketInit(true)
                })
            })
        },

        joinSocket() {
            const {nickname, headimg} = this.getNicknameHeadimg()

            const data = {
                active_id: this.id,
                userid: this.userid,
                nickname,
                headimg,
                message: '进入活动'
            }
            bs_socket.socketTask.send({
                data: JSON.stringify(data)
            })
        },

        receiveMessages(message) {
            message = JSON.parse(message)
            if (message.active_id !== this.id) return false
            this.$refs.lffBarrage.add({item: message})
        },


        async getSimpleDetail(id) {
            this.$uni.showLoading('数据加载中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/active_details_simple',
                data: {
                    active_id: id
                }
            })
            this.loading = false
            uni.hideLoading()

            if (!res || !res.data || !res.data.active_details) {
                this.loading = false
                uni.hideLoading()
                this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                return false
            }


            if (res.data.active_more_data) {
                const active_more_data = res.data.active_more_data
                this.active_more_data = active_more_data
                if (active_more_data.technology_support) {
                    this.technology_support = res.data.active_more_data.technology_support
                }
                if (active_more_data['active_conf_set']) {
                    const active_conf_set = active_more_data['active_conf_set']
                    if (active_conf_set.headimg_plugin && active_conf_set.headimg_plugin.length) {
                        this.headimg_plugin = active_conf_set.headimg_plugin
                    }
                }
            }


            const detail = res.data.active_details


            this.detail = detail

            my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)


            if (detail.conf) {
                const conf = detail.conf

                if (!this.screen_pic && conf.active?.screen_pic) {
                    this.screenPicShow(conf.active.screen_pic)
                }

                if (conf.active && conf.active?.audio_src) {
                    this.audioInit(conf.active.audio_src)
                }

                if (conf.must_submit) {
                    const must_submit = conf.must_submit
                    if (must_submit.length) {
                        this.must_submit = must_submit.map(item => ({...item, value: item.value || ''}))
                    }
                }
            }

            if (detail.rank_set) {
                const rank_set = detail.rank_set
                // 是否纯净版，纯净版显示tab栏，隐藏返回首页按钮
                if (rank_set.shield_other && !this.show_tab) {
                    this.show_tab = true
                }
            }


            this.addLookRecords()

            if (detail.name) this.$uni.setNavigationBarTitle(detail.name)
        },

        toRecord(myself) {
            let url = './run_list?id=' + this.id
            if (this.detail.rank_set?.closed_AD) url += `&close_ad=1`
            if (myself) url += '&myself=1'
            this.$uni.navigateTo(url)
        },

        lookCertificate() {
            if (!this.detail?.conf?.active?.certificate_mileage) 
                return this.$uni.showToast('活动未设置证书目标里程')
            
            this.$uni.navigateTo(`/pages/activity/other/certificate?id=${this.id}&active_types=${this.detail.types}`)
        },

        toIdeaAvatar(item) {
            if (!item.img_list?.length) return this.$uni.showModal('活动未配置头像图片列表，无法使用此功能')

            let path = '/pages/idea_avatar/pendant/pendant'
            if (item.key === 'beijing') path = '/pages/idea_avatar/background/background'
            path += `?id=${this.id}`
            if (this.detail.rank_set?.closed_AD) path += '&closed_AD=1'

            this.$uni.navigateTo(path, {
                success: res => res.eventChannel.emit('img_list', item.img_list)
            })
        },


        screenPicShow(src) {
            let isH5 = false
            // #ifdef H5
            isH5 = true
            // h5不显示开屏图
            // #endif
            if (isH5) return

            uni.hideLoading()
            this.screen_pic = src
            this.screen_pic_count_down = 5
            this.screen_pic_show = true
            interval = setInterval(() => {
                this.screen_pic_count_down--
                if (this.screen_pic_count_down <= 0) this.skipScreen()
            }, 1000)
        },
        skipScreen() {
            clearInterval(interval)
            this.screen_pic_count_down = 0
            this.screen_pic_hide = true

            setTimeout(() => {
                this.screen_pic_show = false
                this.screen_pic_hide = false
                if (this.loading) this.$uni.showLoading('数据加载中...')

                this.passwordDialogShow()
                if (this.from_tab) this.show_tab = true

                const enter_types = this.detail?.conf?.active?.enter_types || 0
                if (this.detail.rank_set?.batch_import && enter_types === 4 && !this.is_joining) {
                    this.$refs.input_username_password.open({
                        detail: this.detail,
                        success: () => {
                            this.$uni.showLoading()
                            this.getDetail()
                        }
                    })
                }
            }, 500)
        },
        analysisScene(scene) {
            const sceneStr = decodeURIComponent(scene)
            console.log(sceneStr)
            const id = this._utils.getUrlParams('id', sceneStr)
            console.log('id===', id)
            if (!id) {
                uni.hideLoading()
                return this.$uni.showModal('从二维码获取id失败')
            }

            this.getActiveId(id)
        },

        audioInit(src) {
            innerAudioContext = uni.createInnerAudioContext()
            innerAudioContext.autoplay = true
            innerAudioContext.loop = true
            innerAudioContext.src = src

            innerAudioContext.onPlay(() => {
                this.audio_play = true
            })
            innerAudioContext.onPause(() => {
                this.audio_play = false
            })
            innerAudioContext.onError((res) => {
                console.log('背景音乐播放失败')
                console.log(res.errMsg)
                console.log(res.errCode)
            })
        },
        audioControl() {
            console.log(innerAudioContext.paused)
            const paused = innerAudioContext.paused
            paused ? innerAudioContext.play() : innerAudioContext.pause()
        },

        async getActiveId(id) {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.active_list/get_long_active_id_by_short_id',
                data: {id}
            })

            const long_id = res?.data?.['long_active_id']
            if (!long_id) return this.$uni.showModal(res && res.info || '长id获取失败')

            this.id = long_id
            this.getDetail()
        },

        getDetail(just_update = false) {
            this.xwy_api.getActivityDetail(this.id, res => {
                if (!res?.data?.active_details) {
                    this.loading = false
                    uni.hideLoading()
                    this.error = (res.info || '活动详情获取失败。') + '活动id：' + this.id
                    return
                }

                // #ifndef H5
                this.$refs.expirationReminder.open(res.data.active_details)
                // #endif

                if (res.data.active_more_data) {
                    const active_more_data = res.data.active_more_data
                    this.active_more_data = active_more_data
                    if (active_more_data.technology_support) {
                        this.technology_support = res.data.active_more_data.technology_support
                    }

                    const headimg_plugin = active_more_data['active_conf_set']?.headimg_plugin
                    if (headimg_plugin?.length) this.headimg_plugin = headimg_plugin
                }


                const detail = res.data.active_details
                app['globalData'].activity_detail = detail
                this.detail = detail
                my_storage.setActivityCloaeAdStorage(this.id, detail.rank_set?.closed_AD)

                if (detail.conf) {
                    const conf = detail.conf

                    if (conf.active) {
                        const active = conf.active
                        if (!this.screen_pic && active.screen_pic) this.screenPicShow(active.screen_pic)
                        if (active.audio_src) this.audioInit(conf.active.audio_src)
                        if (active.news?.news_id) this.changeDetailContent(active.news.news_id)
                    }

                    if (conf.must_submit) {
                        const must_submit = conf.must_submit
                        if (must_submit.length) {
                            this.must_submit = must_submit.map(item => ({...item, value: item.value || ''}))
                        }
                    }
                }

                if (detail.rank_set) {
                    const rank_set = detail.rank_set
                    // 是否纯净版，纯净版显示tab栏，隐藏返回首页按钮
                    if (rank_set.shield_other && !this.show_tab) {
                        this.show_tab = true
                        this.$uni.hideHomeButton()

                        // 更新纯净版缓存信息
                        this._utils.updateShieldOtherInfo(this.detail)
                    }
                    // OA开启了 zs+110 关闭排行榜点击头像查看别人记录  只能管理员和自己才能查看轨迹地图
                    if (rank_set['closed_user_exchange_list']) {
                        this.openMotionRouteSetShow = false
                        this.openMotionRoute = false
                    }
                }


                this.addLookRecords()

                if (detail.name) {
                    this.$uni.setNavigationBarTitle(detail.name)
                }

                if (!just_update && app['globalData'].userid === detail.userid) this.is_my_activity = true

                this.getUserStatus()
            })
        },


        addLookRecords() {
            const detail = this.detail
            const value = {
                active_id: detail.active_id,
                name: detail.name,
                types: detail.types,
                logo: detail.logo || this.xwy_config.active_default_logo,
                look_time: new Date().getTime()
            }

            if (detail.organizer) value.organizer = detail.organizer
            my_storage.addActivityLookRecords(value)
        },


        async getUserStatus() {
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/user_attend_details',
                data: {
                    active_id: this.id
                }
            })

            if (res?.data?.user_details) {
                const attend_details = res.data.user_details
                this.user_details = attend_details
                this.is_joining = true
                this.checked = attend_details.checked || 0

                this.loading = false
                uni.hideLoading()

                if (attend_details.nickname) this.username = attend_details.nickname
                if (attend_details.headimg) this.headimg = attend_details.headimg
                if (attend_details.sex) this.sex = attend_details.sex
                if (attend_details.team_details && this.detail?.rank_set?.team_group_open) {
                    this.team_id = attend_details.team_details.id
                    this.team_name = attend_details.team_details.name
                }

                if (attend_details.must_submit) {
                    this.must_submit.forEach((v, i) => {
                        attend_details.must_submit.forEach(vv => {
                            if (vv.title === v.title) this.$set(this.must_submit[i], 'value', vv.value)
                        })
                    })
                }

                // this.getMissSubmitRun()
            } else {
                this.no_attend = true
                this.loading = false
                uni.hideLoading()

                if (this.screen_pic_show) return false

                this.passwordDialogShow()

                const enter_types = this.detail?.conf?.active?.enter_types || 0
                if (this.detail.rank_set?.batch_import && enter_types === 4) {
                    this.$refs.input_username_password.open({
                        detail: this.detail,
                        success: () => {
                            this.$uni.showLoading()
                            this.getDetail()
                        }
                    })
                    return false
                }
            }

            this.detail?.rank_set?.['barrage'] && this.webSocketInit()
        },


        // 检查是否有意外退出未提交的跑步记录
        getMissSubmitRun() {
            const data = uni.getStorageSync(`run_storage_${this.id}`)
            if (!data) return false
            const submit_data = data.submit_data
            if (!submit_data || !submit_data.meter || !submit_data.seconds) {
                this.removeMissSubmitRun()
                return false
            }

            const mileage_limit = Number(this.detail.conf?.active?.mileage_limit || 0)
            if (mileage_limit && (submit_data.meter / 1000) < this.mileage_limit) {
                 this.removeMissSubmitRun()
                return false
            }

            const pace_limit = Number(this.detail.conf?.active?.pace_limit || 0)
            if (pace_limit && submit_data.pace < this.pace_limit) {
                this.removeMissSubmitRun()
                return false
            }
            const pace_max_limit = Number(this.detail.conf?.active?.pace_max_limit || 0)
            if (pace_max_limit && submit_data.pace > this.pace_max_limit) {
                this.removeMissSubmitRun()
                return false
            }


            const seconds2time = seconds => {
                const hh = Math.floor(seconds / 3600).toString().padStart(2, '0')
                seconds %= 3600;
                const mm = Math.floor(seconds / 60).toString().padStart(2, '0')
                seconds %= 60;
                const ss = seconds.toString().padStart(2, '0')
				return `${hh}:${mm}'${ss}"`
			}

            this.missSubmitRunData = {
                meter: (submit_data.meter / 1000).toFixed(2),
                seconds: seconds2time(submit_data.seconds),
                pace: seconds2time(submit_data.pace),
                calories: submit_data.calories ? Math.floor(submit_data.calories / 100) : null
            }
            this.missSubmitRunSubmitData = submit_data

            this.$nextTick(() => this.$refs.missSubmitPopup.open())

            return true
        },

        removeMissSubmitRun() {
            uni.removeStorageSync(`run_storage_${this.id}`)
        },

        async removeMissSubmitRunData() {
            const res = await this.$uni.showModal(`确定删除未提交的运动记录？`, {showCancel: true})
            if (!res.confirm) return

            this.removeMissSubmitRun()
            this.$refs.missSubmitPopup.close()
            this.$uni.showToast('已删除')
        },

        async submitMissRunData() {
            const submit_data = this.missSubmitRunSubmitData
            submit_data.meter = Math.floor(submit_data.meter)
            if (!this.openMotionRoute) submit_data.hide_map = 1

            this.$uni.showLoading('提交中...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.running.user/submit_running_data',
                data: {
                    submit_data: this._utils.base64['encode'](JSON.stringify(submit_data)),
                    active_id: this.id
                }
            })
            uni.hideLoading()

            if (!res?.status) return this.$uni.showModal(res?.info || '提交失败')

            this.$refs.missSubmitPopup.close()
            this.removeMissSubmitRun()
            this.$uni.navigateTo(`./run_success?meter=${submit_data.meter}&id=${this.id}`)
        },


        async changeDetailContent(news_id) {
            const res = await this.xwy_api.request({
                url: "front.news/news_details",
                data: {news_id}
            })

            uni.hideLoading()

            const details = res?.data?.['news_details']
            if (!details) {
                return this.$uni.showModal(res.info || '文章内容获取失败', {success: () => uni.navigateBack()})
            }

            if (details.video_url) {
                let video_type = 'txv_id'
                if (details.video_url.startsWith('http://') || details.video_url.startsWith('https://')) {
                    video_type = 'http'
                }
                details.video_type = video_type
            }

            if (details.content) details.content = this._utils.newsContentInit(details.content)

            this.news_detail = details
        },


        passwordDialogShow() {
            if (this.detail.conf.active.password && this.no_attend) {
                const passwordDialogShow = () => {
                    this.password_dialog_show = true
                    const password = my_storage.getActivityPassword(this.id)
                    if (password) {
                        this.password = password
                        this.checkActivityPassword(password)
                    }
                    const timeout = setTimeout(() => {
                        this.$refs.input_password.open()
                        clearTimeout(timeout)
                    }, 30)
                }

                // 体验版不需要输入密码
                if (app['globalData'].evn_version === 'trial') {
                    return this.$uni.showModal('此活动设置了活动密码，请勿报名参与活动！！！', {
                        showCancel: true,
                        cancelText: '进入活动',
                        confirmText: '输入密码',
                        success: res => {
                            res.confirm && passwordDialogShow()
                        }
                    })
                }

                passwordDialogShow()
            }
        },

        passwordInputConfirm(val) {
            if (!val) {
                // 纯净版取消输入密码出现去个人中心选项
                if (this.detail?.rank_set?.shield_other) {
                    return this.$uni.showModal('请输入密码', {
                        showCancel: true,
                        cancelText: '个人中心',
                        confirmText: '重新输入',
                        success: res => {
                            if (res?.confirm) {
                                return this.$refs.input_password.open()
                            }

                            this.$uni.navigateTo('/pages/user/user')
                        }
                    })
                }

                return this.$uni.showModal('请输入密码', {success: () => this.$refs.input_password.open()})
            }
            this.checkActivityPassword(val)
        },

        async checkActivityPassword(password) {
            this.$uni.showLoading('密码验证中...')

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/check_active_password',
                data: {
                    active_id: this.id,
                    password
                }
            })
            uni.hideLoading()

            if (res?.status) {
                my_storage.rememberActivityPassword(this.id, password)
                this.$refs.input_password.close()
                return this.$uni.showToast('密码正确', 'success')
            }


            await this.$uni.showModal(res && res.info || '密码错误', {
                success: () => this.$refs.input_password.open()
            })
        },

        passwordInputClose() {
            this.copy(this.id, true)

            if (getCurrentPages().length > 1) return uni.navigateBack()

            // 纯净版并且没有上一页面，重新弹出输入密码窗口
            if (app['globalData'].shop_info?.extend_set?.shield_other_active?.active_id) {
                return this.$uni.showModal('请输入活动密码', {success: () => this.$refs.input_password.open()})
            }

            this.$uni.reLaunch('/pages/index/index')
        },


        async updateHeadimg() {
            this.$uni.navigateTo(`/pages/other/headimg-list/headimg-list?active_id=${this.id}`, {
                events: {
                    newImg: async obj => {
                        if (!obj?.src) return
                        this.$uni.showLoading('修改中...')
                        if (!obj.temp) return this.updateAttendDetail(obj.src)
                        const data = {
                            temp_data: {
                                path: obj.src
                            },
                            is_temp: 5
                        }
                        if (obj.size) data.temp_data.size = obj.size
                        const headimg = await this.xwy_api.uploadOneImage(data)
                        await this.updateAttendDetail(headimg)
                    }
                }
            })
        },

        joinActivity() {
            if (this.loading) return
            if (this.detail.conf?.active?.submit) {
                const now_time = new Date().getTime()
                const {begin, end} = this.detail.conf.active.submit
                if (begin) {
                    const begin_time = new Date(begin.replace(/-/g, '/')).getTime()
                    if (now_time < begin_time) return this.$uni.showToast(`活动于${begin}开始报名`, 'none', 3000)
                }
                if (end) {
                    const end_time = new Date(end.replace(/-/g, '/')).getTime()
                    if (now_time > end_time) return this.$uni.showToast(`活动于${end}截止报名`, 'none', 3000)
                }
            }

            this.join_popup_show = true
        },

        updateAttendDetailShow() {
            this.update_attend_details = true
            this.join_popup_show = true
        },

        mustTextValueChange(e, index) {
            const item = this.must_submit[index]
            item.value = e.detail.value
            this.$set(this.must_submit, index, item)
        },

        mustValueChange(e, index) {
            const item = this.must_submit[index]
            item.value = item.options[e.detail.value].text
            this.$set(this.must_submit, index, item)
        },

        changeTeam() {
            this.$uni.navigateTo(`/pages/activity/admin/team_list?selteam=1&id=${this.id}`, {
                events: {
                    setTeam: team => {
                        this.team_id = team.team_id
                        this.team_name = team.team_name
                    }
                }
            })
        },


        cancelJoin() {
            this.join_popup_show = false
            this.update_attend_details = false
        },

        joinAjax() {
            const {detail, sex, update_attend_details} = this
            if (detail.rank_set?.['select_sex'] && detail.conf?.active?.sex_required && !sex) {
                return this.$uni.showToast('请选择性别')
            }

            if (update_attend_details) return this.updateAttendDetail()

            this.joining()
        },


        getMustSubmitData() {
            const must_submit = JSON.parse(JSON.stringify(this.must_submit))
            for (let i = 0; i < must_submit.length; i++) {
                const v = must_submit[i]
                v.options && delete v.options
                if (v.rules === 1 && v.value === '') {
                    this.$uni.showToast(`请${v.types === 2 ? '选择' : '输入'}${v.title}`)
                    return false
                }

                if (v.types === 3 && v.value && v.value.length !== 11) {
                    this.$uni.showToast(`请输入正确的${v.title}`)
                    return false
                }
            }

            let must_submit_str = JSON.stringify(must_submit)
            must_submit_str = must_submit_str.replace(/·/g, '-')
            return this._utils.base64['encode'](must_submit_str)
        },

        async updateAttendDetail(headimg) {
            const data = {active_id: this.id}

            if (headimg) data.headimg = headimg
            if (this.sex) data.sex = this.sex
            if (this.team_id) data.team_id = this.team_id

            const must_submit = this.getMustSubmitData()
            if (must_submit === false) return false
            data.must_submit = must_submit

            this.$uni.showLoading('修改中...')

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/update_attend_details',
                data
            })

            if (!res.status) return this.$uni.showModal(res.info || '修改失败')

            this.$uni.showToast('修改成功', 'success')

            this.cancelJoin()
            this.getDetail()
        },

        async joining() {
            const {detail, team_id, id, must_submit, username, sex} = this
            if (detail?.rank_set?.team_group_open && detail.conf.active.team_required && !team_id) {
                return this.$uni.showToast('请选择队伍')
            }

            const data = {active_id: id}

            if (must_submit?.length) {
                const must_submit = this.getMustSubmitData()
                if (must_submit === false) return false
                data.must_submit = must_submit
            }

            data.nickname = username
            if (sex) data.sex = sex
            if (team_id) data.team_id = team_id

            this.loading = true
            this.$uni.showLoading('报名中...')

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/submit_attend_active',
                data
            })
            this.loading = false
            uni.hideLoading()

            if (!res?.status) return this.$uni.showModal(res.info || '报名失败', {title: '报名失败'})

            this.join_popup_show = false
            this.$uni.showToast(res.info || '报名成功', 'success')

            setTimeout(() => {
                this.$uni.showLoading()
                this.getDetail()
            }, 1000)
        },

        async toRun() {
            if (!this.is_joining) return this.$uni.showToast('还未参与活动')

            if (this.detail.conf.active.enter_types === 3 && !this.checked) {
                return this.$uni.showToast('报名未审核')
            }

            if (!await runTimeCheck.check(this.detail.conf.active.run_time_limit, this.detail.conf.active.run_time_list)) {
                return this.$uni.showToast('不在运动时段内, 不能运动。')
            }

            if (!activity_tool.actionCheck({
                is_joining: this.is_joining,
                checked: this.checked,
                begin_time: this.detail.begin_time,
                end_time: this.detail.end_time
            })) return

            if (this.getMissSubmitRun()) return

            this.$uni.navigateTo('./run?id=' + this.id)
        },


        toTopList() {
            this.$uni.navigateTo('./ranking-list?id=' + this.id)
        },

        showActiveSharePopup() {
            this.$refs.activeShare.open({
                page: 'pages/running/user/detail',
                scene: 'id=' + this.detail.id,
                qrcode_logo: this.detail.rank_set?.closed_AD && this.detail.conf?.active?.qrcode_logo
            })
        },

        copy(data, hide_toast = false) {
            this.$uni.setClipboardData(data, hide_toast ? '' : '复制成功')
        },

        uniPopupClose(ref) {
            this.popup_open = false
            this.$refs[ref].close()
        },

        uniPopupOpen(ref) {
            this.popup_open = true
            this.$refs[ref].open()
        }

    }
}
</script>

<style scoped>
.page {
    min-height: 100vh;
    box-sizing: border-box;
    padding-bottom: 80px;
}

.bdb-10 {
    border-bottom: 10px solid #f8f8f8;
}

.icon-list {
    position: sticky;
    top: 0;
    z-index: 9;
    border-top: 1px solid #eee;
}

.icon-item {
    padding: 10px 0;
    width: calc(100% / 4);
    /* border-top: 1px solid #eee; */
    box-sizing: border-box;
}


.headimg {
    width: 80px;
    height: 80px;
    border-radius: 50%;
}


.join-btn {
    width: 250px;
    height: 40px;
    line-height: 40px;
    text-align: center;
    border-radius: 20px;
}

.join-popup {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, .7);
    z-index: 9999;
}

.join-popup-c {
    border-radius: 10px;
    overflow: hidden;
}


.join-input {
    margin: 10px;
    padding: 10px;
    border: 1px solid #bbb;
    border-radius: 5px;
    width: 70vw;
    max-width: 400px;
}

.join-popup-btns {
    border-top: 1px solid #eee;
}

.join-popup-btn-cancel, .join-popup-btn-confirm {
    width: 50%;
    box-sizing: border-box;
    line-height: 44px;
}

.join-popup-btn-cancel {
    border-right: 1px solid #eee;
}


.uni-popup-info {
    position: relative;
    width: 280px;
    border-radius: 10px;
    overflow: hidden;
}

.popup-close {
    position: absolute;
    padding: 5px;
    z-index: 999;
    left: 50%;
    bottom: -5px;
    margin-left: -12px;
}

.detail-popup {
    width: 95vw;
    padding-bottom: 15px;
}

.audio {
    position: fixed;
    border: 1px solid #fff;
    top: 20px;
    right: 20px;
    background-color: rgba(0, 0, 0, .5);
    width: 40px;
    height: 40px;
    border-radius: 50%;
    z-index: 9;
}

.audio_rotate {
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    100% {
        transform: rotate(360deg);
    }
}

.miss-submit-popup {
    width: 320px;
}
.miss-submit-popup .radio {
    transform: scale(0.7);
    position: relative;
    top: -1px;
    left: 5px;
}

.miss-submit-popup .submit-button {
    width: 200px;
    line-height: 44px;
    border-radius: 22px;
}
</style>
