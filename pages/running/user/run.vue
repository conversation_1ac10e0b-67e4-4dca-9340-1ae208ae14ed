<template>
    <view class='content'>
        <view>
            <map :longitude="lng" :latitude="lat" show-location="true" :polyline="polyline"
                 :enable-satellite="enable_satellite">
                <!-- 未开始运动时显示的遮罩弹框 -->
                <!--<view v-if="is_auth >= 0 && !is_run && !show_downTime"
                    class="start-btn bg-white radius50 p_3 blk_shadow_005" @click.stop="start_run">
                    <view class="width100 height100 radius50 f-d-c-j-a-c gre_bgcolor">
                        <image class="width30px height30px" src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/run.png"/>
                        <view class="pt_3">{{is_auth == 1 ? '开始' : '开启设置'}}</view>
                    </view>
                </view>-->
                
                <!--由于广晟那边想要3D按钮，所以需要做成图片，但是由于广晟那边想要快点看到效果，所以先不做活动自定义设置按钮图片，先统一使用默认图片， 上面的旧代码先不删，以防万一-->
                <view v-if="is_auth >= 0 && !is_run && !show_downTime"
                      class="new-start-btn radius50" @click.stop="start_run">

                    <image class="button-image" mode="aspectFill" 
                           :src="`https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/run${is_auth === 1 ? '2' : '1'}.png`"/>
                </view>
                <!-- 点击开始后的倒计时 -->
				<uni-transition :show="show_downTime" :mode-class="['fade', 'zoom-out']">
					<view
						class="relative org_bgcolor f-j-a-c"
						style="padding-bottom: 10vh; box-sizing: border-box; width: 100vw; height: 100vh;"
						:style="{fontSize: down_time > 0 ? '300rpx' : '200rpx'}"
					>
						<view v-if="down_time === 3" class="countdown-num"> 3 </view>
						<view v-if="down_time === 2" class="countdown-num"> 2 </view>
						<view v-if="down_time === 1" class="countdown-num"> 1 </view>
						<view v-if="down_time <= 0" class="countdown-go"> GO </view>
					</view>
				</uni-transition>
                <!-- 运动时的全屏计时 -->
                <view
					v-if="is_run"
					class="screen-run width100"
					:class="is_full ? 'full-screen-run' : 'window-screen-run border-10-0'"
					style="bottom: 0"
				>
                    <view v-if="weight" class="calories" @click.stop="showExplain('calories')">
                        <uni-icons type="fire" color="#ffffff"/>
                        <text style="padding-right: 2px;">{{ calories || 0 }}</text>
                        <text class="font12">千卡</text>
                    </view>
                    <view class="f-d-c-j-a-c" :style="{'height': is_full ? '300px' : '150px'}">
                        <!-- 公里 -->
                        <view class="f-d-c-j-a-c width100" :class="{'p1-0': is_full}"  @click.stop="change_full">
                            <view :class="is_full ? 'font4' : 'big-sy'">{{(distance / 1000).toFixed(2)}}</view>
                            <view class="km-fz" :class="is_full ? 'font1_1' : ''">公里</view>
                        </view>
                        <!-- 用时和配速 -->
                        <view class="f width100" :class="{'p1-0': is_full}" @click.stop="change_full">
                            <view class="width50 f-d-c-j-a-c">
                                <view :class="is_full ? 'font1_8' : 'small-sy'">
                                    {{ timeText }}
                                </view>
                                <view class="km-fz" :class="is_full ? 'font1_1' : ''">用时</view>
                            </view>
                            <view class="width50 f-d-c-j-a-c" @click.stop="showExplain('speed')">
                                <view :class="is_full ? 'font1_8' : 'small-sy'">
                                    {{ speedTimeText }}
                                </view>
                                <view class="km-fz" :class="is_full ? 'font1_1' : ''">配速</view>
                            </view>
                        </view>
                    </view>
                    <!-- 按钮组 -->
                    <view
						class="relative"
						style="height: 120px;"
						:animation="is_full ? animation.fun_window.full : animation.fun_window.no_full"
					>
                        <view class="btn-box f-a-c width100">
                            <view class="width33 f-j-a-c" @click.stop="change_full">
                                <!--健步走活动过来运动的不显示黑黑的全屏，客户(广晟)不喜欢-->
                                <image
                                    v-if="active_types !== 2"
									class="width50px height50px"
									:src="is_full ? 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/map.png' : 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/pull.png'"
								/>
                            </view>
                            <view class="width33 f-j-a-c">
                                <view
									class="f-d-c-j-a-c width90px height90px radius50 font_9"
                                    style="background-color: #ddd; color:#444"
									@click.stop="end_run"
								>
                                    <image
										class="width30px height30px"
										src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/end.png"
									/>
                                    <view class="pt_2">结束</view>
                                </view>
                            </view>
                            <view class="width33 f-j-a-c">
                                <view
									class="org_bgcolor f-d-c-j-a-c width60px height60px radius50 font_9"
                                    @click.stop="change_pause"
								>
                                    <image
										v-if="is_pause"
										class="width30px height30px"
										src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/start.png"
									/>
                                    <image
										v-if="!is_pause"
										class="width30px height30px"
										src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/pause.png"
									/>
                                    <view>{{is_pause ? '继续' : '暂停'}}</view>
                                </view>
                            </view>
                        </view>
                    </view>
                    <!-- 上拉图标 -->
                    <!-- <image v-if="!is_full" class="on-pull-btn" src="https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/jbz_xcx/static/image/pull.png" @click.stop="change_full">
                    </image> -->
                </view>
            </map>
        </view>

        <uni-popup ref="submitPopup">
            <view class="popup-- bg-white radius10 p10">
                <view class="p10 text-center color-title">运动数据</view>
                <view class="flex-all-center p10">
                    <view class="p10 font18 color-content">
                        <view>距离: {{ Number((distance / 1000).toFixed(2)) }}km</view>
                        <view>用时: {{ timeText }}</view>
                        <view>配速: {{ speedTimeText }}</view>
                        <view>消耗: {{ calories }}千卡</view>
                    </view>
                </view>
                <view v-if="open_motion_route_set_show" class="flex-all-center p10">
                    <view @click="open_motion_route = !open_motion_route">

                        <radio class="radio" :checked="open_motion_route" />
                        <text class="color-sub font14">公开运动轨迹</text>
                    </view>
                </view>
                
                <view class="flex-all-center">
                    <view class="submit-button bg-primary color-white text-center" @click="submitReady">
                        提交运动记录
                    </view>
                </view>

                <view class="flex-all-center">
                    <view class="p10 color-sub font14" @click="keepMoving">继续运动</view>
                </view>
            </view>
        </uni-popup>

        <view v-show="!is_full && !show_downTime" class="map-type-switch flex-all-center" 
              @click="enableSatelliteChange">
            <text :class="'iconfont color-white font28 icon-' + (enable_satellite ? 'map-1' : 'earth')"></text>
        </view>
    </view>
</template>

<script>
	const app = getApp()
    import base64 from '@/utils/base64.js'
    import runTimeCheck from '../run-time-check'

    const DEFAULT_POLYLINE_ITEM = {
		points: [],
		color: '#53BA84',
		width: 5
	}
	const SoundEffects = {
		CountDown: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/music/sport/di.mp3',
		Go: 'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/music/sport/go.mp3'
	}

    export default {
        data() {
            return {
                lat: 39.908810,
                lng: 116.397480,
                is_auth: -1, // 是否授权了
                is_run: false, // 是否正在运动中

                polyline: [JSON.parse(JSON.stringify(DEFAULT_POLYLINE_ITEM))],

                show_downTime: false,   // 是否倒计时，橙色页面 3S倒计时
                down_time: 4, // 倒计时的时间
                is_full: false, // 运动中，是否显示全屏

                time: 0,
                distance: 0, // 计算出的公里数
                
                is_pause: false, // 是否暂停计时
				animation: {
					fun_window: {
						full: {},
						no_full: {},
					}
				},

                active_types: 12,   // 活动类型 默认是12 运动轨迹活动
                open_motion_route_set_show: true,  // 是否显示运动轨迹公开设置
                open_motion_route: true, // 提交记录时是否公开运动轨迹  默认公开， 健步走活动过来的默认不公开
                // timeList: [],
                enable_satellite: uni.getStorageSync('run_enable_satellite_' + app.globalData['who']) || false,
                weight: 0
            }
        },
		watch: {
			is_full: function(val) {
				this.setNavigationBarColor()
				this.setFullWindowAnimation(val)
			},
			show_downTime: function() {
				this.setNavigationBarColor()
			},
			is_run: function(val) {
				this.setNavigationBarColor()
				if (val) {
					wx.enableAlertBeforeUnload({
						message: '正在运动，是否确定退出？退出后本次运动数据不会保存，也无法找回。'
					})
				} else {
					wx.disableAlertBeforeUnload()
				}
			},
		},
        
        computed: {
            timeText() {
                return this.seconds2time(this.time)
            },
            speedTime() {
                return this.distance ? Math.floor(this.time / (this.distance / 1000)) : 0
            },
            speedTimeText() {
                return this.seconds2time(this.speedTime)
            },
            calories() {
                const {weight, distance} = this
                if (!weight || !distance) return 0
                return Math.floor(weight * (distance / 1000) * 1.036)
            }
        },
        
        
		onUnload() {
			this.pause_run()
            this.stopLocationUpdate()
            this.setKeepScreenOn(false)
            // this.removeRunStorage()
		},
        onLoad(options) {
            if (options.id) this.id = options.id

            this.$uni.setNavigationBarTitle('运动')
            this.init()
        },
        methods: {
            showExplain(type) {
                this._utils.showRunExplain(type)
            },

            setKeepScreenOn(status) {
                uni.setKeepScreenOn({
                    keepScreenOn: status,
                    success: res => {
                        console.log(`setKeepScreenOn ${status} success:`, res)
                    },
                    fail: err => {
                        console.log(`setKeepScreenOn ${status} fail:`, err)
                    }
                })  
            },

            async init() {
                await this.getActivityData()
				this.AudioContext = uni.createInnerAudioContext()
                this.getSetting()
                await this.getWeight()
            },
            
            async getWeight() {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.lose_weight.user/user_weight_height_list',
                    data: {
                        myself: 1,
                        page: 1,
                        perpage: 1
                    }
                })
                const weight = res?.data?.list?.data?.[0]?.weight
                if (weight) this.weight = weight / 1000
            },

            async getActivityData() {
                if (!this.id) return
                this.$uni.showLoading()
                let activity_detail = app.globalData['activity_detail']
                if (!activity_detail || activity_detail.active_id !== this.id) {
                    const res = await this.xwy_api.request({
                        url: 'front.flat.sport_step.active_list/active_details',
                        data: { active_id: this.id }
                    })
                    activity_detail = res?.['data']?.['active_details'] || {}
                }
                if (activity_detail?.types) {
                    this.active_types = activity_detail.types
                    if (activity_detail.types === 2) this.open_motion_route = false
                }
                
                
                // OA开启了 zs+110 关闭排行榜点击头像查看别人记录  只能管理员和自己才能查看轨迹地图
                if (activity_detail.rank_set?.['closed_user_exchange_list']) {
                    this.open_motion_route_set_show = false
                    this.open_motion_route = false
                }
                
                this.exchange_kilo_unit = activity_detail.conf?.active?.kilo_unit || '里'
                this.pace_limit = Number(activity_detail.conf?.active?.pace_limit || 0)
                this.pace_max_limit = Number(activity_detail.conf?.active?.pace_max_limit || 0)
                this.mileage_limit = Number(activity_detail.conf?.active?.mileage_limit || 0)

                this.activeDetails = activity_detail
                uni.hideLoading()
            },

            getSetting() {
                uni.getSetting({
                    success: res => {
                        console.log('获取授权', res)
                        if (res.authSetting['scope.userLocation']) {
                            this.getLocation()
                        }
                        if (res.authSetting['scope.userLocationBackground']) {
                            this.is_auth = 1
                            return
                        }
                        this.is_auth = 0
						this.authorize()
                    }
                })
            },

            getLocation() {
                uni.getLocation({
                    type: 'gcj02',
                    success: res => {
                        console.log('获取地理位置', res)
                        this.lat = res.latitude
                        this.lng = res.longitude
                    }
                })
            },

            openSetting() {
				console.log('openSetting')
                uni.openSetting({
                    success: (res) => {
                        console.log('openSetting', res)
                        if (res.authSetting['scope.userLocationBackground']) {
                            this.is_auth = 1
                            this.getLocation()
                            return
                        }
                        this.is_auth = 0
                    },
					fail: err => {
						console.log(err)
						this.is_auth = 0
					}
                })
            },

			authorize() {
				uni.authorize({
					scope: 'scope.userLocationBackground',
					success: res => {
						console.log(res)
						this.is_auth = 1
						this.getSetting()
					},
					fail: err => {
						console.log(err)
						uni.showModal({
						    content: '“位置消息”请选择“使用小程序时和离开后”才能正常使用',
						    success: res => {
						        if (res.confirm) this.openSetting()
						    }
						})
					}
				})
			},

            async timeCheck() {
                if (!this.id || !this.activeDetails?.conf?.active?.run_time_list?.length) return true

                return runTimeCheck.check(this.activeDetails.conf.active.run_time_limit, this.activeDetails.conf.active.run_time_list)
            },

            async start_run() {
                if (this.is_auth === 0) {
					this.authorize()
                    // let title = '请选择“使用小程序时和离开后”才能正常使用';
                    // uni.showModal({
                    //     content: title,
                    //     success: res => {
                    //         if (res.confirm) this.authorize();
                    //     }
                    // })
                    return
                }

                if (!await this.timeCheck()) return this.$uni.showToast('不在运动时段内, 不能运动。')

                await this.getRunEndTime()

                await this.init_value()
            },

            // 获取当前运动时间段的运动结束时间。用于后面缓存运动记录的时候判断是否超过运动结束时间，结束了就不继续缓存
            async getRunEndTime() {
                // 不是活动
                if (!this.id) return

                // 没有开启或设置运动时间段
                const limit = this.activeDetails?.conf?.active?.run_time_limit
                const run_time_list = this.activeDetails?.conf?.active?.run_time_list
                if (!limit || !run_time_list?.length) return

                // 关闭了缓存运动记录
                if (this.activeDetails.conf.active.not_save_run_storage) return

                this.runEndTime = await runTimeCheck.getEndTime(run_time_list) || null
            },

            // 倒计时3S 然后进入运动时间
            downTime() {
                this.show_downTime = true
				this.AudioContext.src = SoundEffects.CountDown
				this.down_time = 3
				this.AudioContext.play()
				const interval = setInterval(() => {
					this.down_time--
					if (this.down_time === 0) this.AudioContext.src = SoundEffects.Go
					if (this.down_time < 0) {
						// go的音频比较长，留出300毫秒的时间
						setTimeout(() => {
							this.show_downTime = false
							this.is_run = true
							this.startLocationUpdateBackground()
						}, 300)
						clearInterval(interval)
					}
					this.AudioContext.play()
				}, 1000)
            },

            stopLocationUpdate() {
                uni.offLocationChange(res => console.log('取消监听位置变化', res))
                uni.stopLocationUpdate({
                    complete: res => console.log('停止位置更新', res)
                })  
            },
            
            startLocationUpdateBackground() {
                uni.startLocationUpdateBackground({
                    type: 'gcj02',
                    success: () => {
                        this.is_auth = 1
                        // 监听经纬度
                        this.onLocationChange()
                        // 开始计时
                        this.start_timing()
                    },
                    fail: () => {
                        this.is_auth = 0
                    }
                })
            },

            onLocationChange() {
                uni.onLocationChange(res => {
                    if (this.is_pause) return
                    // 经纬度只需要小数点后六位
					res.latitude = Number(res.latitude.toFixed(6))
					res.longitude = Number(res.longitude.toFixed(6))
                    this.lat = res.latitude
                    this.lng = res.longitude
                    this.get_polyline(res)
                })
            },



            get_polyline({latitude, longitude}) {
                const points = this.polyline[this.polyline.length - 1].points || []
                const points_length = this.polyline[this.polyline.length - 1].points.length

                const location = {latitude, longitude}
                
                if (points_length < 2) {
                    this.addPolylinePoint(points_length, location)
                    this.addPolylinePoint(points_length + 1, location)
                    return
                }

                const start = points[points_length - 1]
                const end = location

                const distance = this._utils.getDistance(start.latitude, start.longitude, end.latitude, end.longitude)
                
                if (this.distanceAnomaly(distance)) return

                // 平均速度异常不记录
                if (this.speedAnomaly(distance)) return
                
                this.distance += distance
                this.addPolylinePoint(points_length, location)
            },

            distanceAnomaly(distance) {
                const MIN_DISTANCE = 10 // 由于onLocationChange返回坐标的频率太高，所以手动过滤一下，距离小于10米不记录

                // 由于GPS信号可能会出现突然抽风跑偏的情况，造成最后公里数偏大导致配速过小无法提交运动记录。距离大于100米不记录。
                // 建议这个值不能小于100，不然会有比较大的几率会出现后面的路线记录不上问题。
                const MAX_DISTANCE = 100

                return distance < MIN_DISTANCE || distance > MAX_DISTANCE
            },
            
            addPolylinePoint(index, location) {
                this.record_time = new Date().getTime()
                this.$set(this.polyline[this.polyline.length - 1].points, index, location)
            },

            addRunStorage() {
                // 不是活动
                if (!this.id) return

                // 没有里程或时间
                if (!this.distance || !this.time) return

                // 活动关闭了缓存运动记录
                if (this.activeDetails?.conf?.active?.not_save_run_storage) return

                // 超过了运动结束时间，不缓存
                if (this.runEndTime && new Date().getTime() > this.runEndTime) return

                // 不符合活动规则的，比如里程数不够，配速太快等，不能缓存
                if (!this.violationCheck().status) {
                    this.removeRunStorage()
                    return
                }

                const submit_data = {
					meter: this.distance,
					seconds: this.time,
					pace: this.speedTime,
					running_map: {
						polyline: this.polyline
					}
				}

                if (this.calories) submit_data.calories = Math.floor(this.calories * 100)

                uni.setStorageSync(`run_storage_${this.id}`, {
                    active_id: this.id,
                    submit_data
                })
            },

            removeRunStorage() {
                uni.removeStorageSync(`run_storage_${this.id}`)
            },

            
            // 速度是否异常  平均每米的速度低于0.1秒则为异常
            speedAnomaly(distance) {
                const time = new Date().getTime() - this.record_time

                // 每米的最低速度(毫秒) 1米100毫秒即为1米0.1秒  10米1秒 100米10秒  如果平均速度低于这个值，则为异常
                const meter_speed = 100
                
                return time / distance < meter_speed
            },

            // 更换全屏
            change_full() {
                // 健步走活动过来运动的不显示黑黑的全屏，客户(广晟)不喜欢
                if (this.active_types === 2) return 
                this.is_full = !this.is_full
            },

			setNavigationBarColor() {
				let frontColor = '#000000', backgroundColor = '#ffffff'
				if (this.is_run && this.is_full) {
					backgroundColor = '#000000'
					frontColor = '#ffffff'
				}
				if (this.show_downTime) {
					backgroundColor = '#faaa4f'
					frontColor = '#ffffff'
				}
				uni.setNavigationBarColor({
					frontColor,
					backgroundColor,
					animation: {
						timingFunc: 'easeOut',
						duration: backgroundColor === '#faaa4f' ? 0 : 300
					}
				})
			},

			setFullWindowAnimation(val) {
				const animation = uni.createAnimation({
					duration: 300,
					timingFunction: 'ease'
				})
				if (val) {
					animation.height('calc(100vh - 300px)').step()
					this.animation.fun_window.full = animation.export()
				} else {
					animation.height(120).step()
					this.animation.fun_window.no_full = animation.export()
				}
			},

			seconds2time(seconds) {
                const hh = Math.floor(seconds / 3600).toString().padStart(2, '0')
                seconds %= 3600;
                const mm = Math.floor(seconds / 60).toString().padStart(2, '0')
                seconds %= 60;
                const ss = seconds.toString().padStart(2, '0')

				return `${hh}:${mm}'${ss}"`
			},

            // 开始运动后 计时
            start_timing() {
                this.startTimeCalculation()
                this.timing = setInterval(() => {
                    this.timeCalculation()
                }, 1000)
            },
            
            
            startTimeCalculation() {
                this.timeList ||= []
                this.timeList.push({s: new Date().getTime()})
            },
            endTimeCalculation() {
                this.timeList[this.timeList.length - 1].e = new Date().getTime()
                this.timeCalculation()
            },
            timeCalculation() {
                const timeList = JSON.parse(JSON.stringify(this.timeList))
                const lastTime = timeList[timeList.length - 1]
                lastTime.e ||= new Date().getTime()
                const allTime = timeList.reduce((acc, cur) => acc + (cur.e - cur.s), 0)
                this.time = Math.floor(allTime / 1000)

                this.addRunStorage()
            },

            // 暂停或继续
            change_pause() {
                this.is_pause = !this.is_pause
                if (this.is_pause) {
                    this.pause_run()
                    return this.endTimeCalculation()
                }
				this.continue_run()
            },

            // 暂停运动
            pause_run() {
                this.setKeepScreenOn(false)
                this.is_pause = true
                // this.stopLocationUpdate()
                this.timing && clearInterval(this.timing)
            },

            // 继续运动
            continue_run() {
                this.setKeepScreenOn(true)
                this.is_pause = false
				// 继续运动，新添加一条路线，用于排除掉暂停期间的路线和里程
				this.polyline.push(JSON.parse(JSON.stringify(DEFAULT_POLYLINE_ITEM)))
                this.startLocationUpdateBackground()
            },

            
            // 由于开发工具上电脑不好移动，需要模拟数据
            setLocalSimulationData() {
                const {evn_version, userid} = app.globalData
                const simulate_userid_list = [132, 1906258, 1906305, 1906442, 2787854]
                const is_simulate_user = simulate_userid_list.includes(userid)
                if (evn_version !== 'develop' || !is_simulate_user || this.distance) return

                this.distance = this._utils.randomNum(10, 10000)
            },

            violationCheck() {
                if (!this.distance) return {status: 0, info: '运动里程为0，无法提交哦'}

                if (this.mileage_limit && (this.distance / 1000) < this.mileage_limit) {
                    return {status: 0, info: `必须达到${this.mileage_limit}km后才可以提交哦。`}
                }

                if ((this.pace_limit && this.speedTime < this.pace_limit) || (this.pace_max_limit && this.speedTime > this.pace_max_limit)) {
                    return {status: 0, info: '系统判断本次为非正常跑步，无法提交。'}
                }

                return {status: 1}
            },
            
            // 结束运动
            async end_run() {
                !this.is_pause && this.endTimeCalculation()
				this.pause_run()
                this.setLocalSimulationData()

                if (!await this.timeCheck()) return this.$uni.showToast('不在运动时段内, 不能提交。')

                const violationCheck = this.violationCheck()
                if (!violationCheck.status) {
                    this.$uni.showModal(violationCheck.info)
                    return
                }
                
                this.$refs.submitPopup.open()

            },
            
            submitReady() {
                this.$refs.submitPopup.close()
                this.stopLocationUpdate()
                this.end_run_post()
            },
            
            keepMoving() {
                this.$refs.submitPopup.close()
                this.change_pause()
            },

            async end_run_post() {
				this.$uni.showLoading()
				const submit_data = {
					meter: Math.floor(this.distance),
					seconds: this.time,
					pace: this.speedTime,
					running_map: {
						polyline: this.polyline
					}
				}
                if (!this.open_motion_route) submit_data.hide_map = 1
                if (this.calories) submit_data.calories = Math.floor(this.calories * 100)
                
                const data = {
                    submit_data: base64['encode'](JSON.stringify(submit_data))
                }
                if (this.id) data.active_id = this.id
                
                this.$uni.showLoading('提交中...')
				const res = await this.xwy_api.request({
					url: 'front.flat.sport_step.running.user/submit_running_data',
					data
				})

				if (!res?.status) {
					uni.hideLoading()
					return this.$uni.showModal(res?.info || '提交失败')
				}

				this.pause_run()

                this.removeRunStorage()

                // 给取消位置监听和添加缓存留出300毫秒的时间
				// setTimeout(() => {
					this.is_run = false
                    this.getOpenerEventChannel?.()?.emit?.('refreshList')

                    let url = `./run_success?meter=${submit_data.meter}`
                    if (this.id) url += `&id=${this.id}`
                    this.$uni.redirectTo(url)
                    uni.hideLoading()
				// }, 300)
            },


            // 初始化值
            async init_value() {
                this.polyline = [JSON.parse(JSON.stringify(DEFAULT_POLYLINE_ITEM))]
                this.time = 0
                this.timing = null
                this.distance = 0 // 公里数
                this.is_pause = false // 是否暂停计时
                
                await this.$uni.showModal('运动期间请勿锁屏')
                
                this.setKeepScreenOn(true)
                this.downTime()
            },

            enableSatelliteChange() {
                this.enable_satellite = !this.enable_satellite
                uni.setStorageSync('run_enable_satellite_' + app.globalData['who'], this.enable_satellite)
            }

        }
    }
</script>

<style scoped lang='scss'>
    .content {
        width: 100%;
        min-height: 100vh;
    }

    map {
        width: 100%;
        min-height: 100vh;
    }

    /*.start-btn {
        position: absolute;
        bottom: 15%;
        left: 50%;
        transform: translateX(-50%);
        width: 100px;
        height: 100px;
    }*/
    
    .new-start-btn {
        position: absolute;
        bottom: 15%;
        left: 50%;
        transform: translateX(-50%);
        width: 120px;
        height: 120px;

        .button-image {
            width: 100%;
            height: 100%;
        }
    }

    .screen-run {
        position: absolute;
        color: #fff;
        
        .calories {
            position: absolute;
            top: 20rpx;
            right: 20rpx;
        }
        
        .km-fz {
            color: #ccc;
        }

        .btn-box {
            position: absolute;
            bottom: 15%;
            left: 0;
        }
    }

	.border-10-0 {
		border-radius: 10px 10px 0 0;
	}

    .full-screen-run {
        background-color: #000;
    }


    .window-screen-run {
        background-color: rgba(0, 0, 0, .7);
    }


    .on-pull-btn {
        position: absolute;
        top: .5rem;
        left: .5rem;
        width: 35px;
        height: 35px;
    }

    .tips {
        position: absolute;
        bottom: calc(15% + 100px);
        left: 50%;
        transform: translateX(-50%);
        width: 6rem;
        background-color: #555;
        color: #fff;
    }

    .small-sy {
        font-size: 24px;
        height: 1.7rem;
        line-height: 1.7rem;
    }

    .big-sy {
        font-size: 34px;
        height: 3.5rem;
        line-height: 3.5rem;
    }

	.radius50 {
	    border-radius: 50%;
	}

	.p_3{
	    padding: .3rem !important;
	}

	.blk_shadow_005 {
	    box-shadow: 0 0 3px 1px rgba(0, 0, 0, 0.05);
	}

	.width100 {
	    width: 100%;
	}

	.height100 {
	    height: 100%;
	}

	.f-d-c-j-a-c {
	    display: flex;
	    justify-content: center;
	    align-items: center;
	    flex-direction: column;
	}

	.gre_bgcolor {
	    background-color: #00B26A !important;
	    color: #fff !important;
	}

	.width30px {
	    width: 30px;
	}

	.height30px {
	    height: 30px;
	}

	.width60px {
	    width: 60px;
	}

	.height60px {
	    height: 60px;
	}

	.pt_3 {
	    padding-top: .3rem !important;
	}

	.relative{
	    position: relative;
	}

	.countdown-num {
		animation: countdown_num .3s linear;
		-webkit-animation: countdown_num .3s linear;
	}
	@keyframes countdown_num {
		from {font-size: 700rpx;}
		to {font-size: 300rpx;}
	}
	@-webkit-keyframes countdown_num {
		from {font-size: 700rpx;}
		to {font-size: 300rpx;}
	}
	.countdown-go {
		animation: countdown_go .3s linear;
		-webkit-animation: countdown_go .3s linear;
	}
	@keyframes countdown_go {
		from {font-size: 400rpx;}
		to {font-size: 200rpx;}
	}
	@-webkit-keyframes countdown_go {
		from {font-size: 400rpx;}
		to {font-size: 200rpx;}
	}

	.org_bgcolor {
	    background-color: #FAAA4F !important;
	    color: #fff !important;
	}

	.f-j-a-c {
	    display: flex;
	    justify-content: center;
	    align-items: center;
	}



	.p1-0 {
	    padding: 10px 0 !important;
	}

	.font4 {
	    font-size: 60px !important;
	}

	.font1_1 {
	    font-size: 14px !important;
	}

	.f {
	    display: flex;
	}

	.font1_8 {
	    font-size: 32px !important;
	}

	.width50 {
	    width: 50% !important;
	}

	.f-a-c {
	    display: flex;
	    align-items: center;
	}

	.width33 {
	    width: calc(100% / 3);
	}

	.width50px {
	    width: 50px;
	}

	.height50px {
	    height: 50px;
	}

	.width90px {
	    width: 90px;
	}

	.height90px {
	    height: 90px;
	}
    
    
    .popup-- {
        width: 320px;
        
        .radio {
            transform: scale(0.7);
            position: relative;
            top: -1px;
            left: 5px;
        }
        
        .submit-button {
            width: 200px;
            line-height: 44px;
            border-radius: 22px;
        }
    }

    .map-type-switch {
        position: fixed;
        right: 10px;
        bottom: 300px;
        width: 40px;
        height: 40px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, .7);
    }
</style>
