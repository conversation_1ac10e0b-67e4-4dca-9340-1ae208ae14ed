<template>
    <view class="page bg-background">
        <view class="top-view">
            <view class="search bg-white flex-kai p10">
                <uni-easyinput class="input bg-background" prefix-icon="search" confirm-type="search"
                               v-model="search_keyword" @confirm="search()" placeholder="输入用户名字搜索"
                               trim="all" :input-border="false"/>
                <view class="search-go color-info pl10" @click="search">搜索</view>
            </view>
        </view>

        <view class="list">
            <view class="item bg-white" v-for="item in list" :key="item.id" @click="checkItem(item)">

                <view class="flex-row">
                    <image v-if="item.headimg" class="headimg" :src="item.headimg" mode="aspectFill"/>

                    <view class="middle">
                        <view class="ptm5 color-title">{{ item.nickname }}</view>
                        <view class="clear clearfix">
                            <view class="fr">
                                <text class="font14 color-light-primary">运动记录</text>
                                <uni-icons type="forward" size="14" color="#5cadff"/>
                            </view>
                        </view>
                    </view>
                </view>

            </view>
        </view>


        <view v-if="!list.length && !more_loading && !init_load" class="text-center"
              style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无用户</view>
        </view>

        <uni-load-more v-if="more_loading" status="loading"></uni-load-more>
        <uni-load-more
            v-if="is_last_page && list.length > 5"
            status="noMore"
            :contentText="{contentnomore: '我是有底线的'}"
        ></uni-load-more>
        <uni-load-more v-if="!is_last_page && !more_loading && !init_load" status="more"></uni-load-more>

    </view>
</template>

<script>
export default {
    data() {
        return {
            init_load: true,
            list: [],
            load_page: 1,
            is_last_page: false,
            more_loading: false,
            search_keyword: ''
        }
    },

    onLoad(params) {
        this.active_id = params.active_id
        if (params.name) this.search_keyword = params.name

        uni.showLoading({
            mask: true
        })
        this.$login.uniLogin(err => {
            if (err && err.errMsg) {
                uni.hideLoading()
                return this.$uni.showModal(err.errMsg, {title: err['errTitle'] || '提示'})
            }

            this.getList(true)
        })
    },


    onPullDownRefresh() {
        if (this.init_load || this.more_loading) {
            uni.stopPullDownRefresh()
            return false
        }
        this.load_page = 1
        this.getList()
        uni.stopPullDownRefresh()
    },


    onReachBottom() {
        if (!this.init_load && !this.more_loading && !this.is_last_page) {
            this.load_page++
            this.getList()
        }
    },


    methods: {
        search() {
            if (!this.search_keyword) return this.$uni.showToast('请输入用户名字')

            this.load_page = 1
            this.getList()
        },

        // initLoad 是否进入页面就搜索，是的话，如果搜索结果只有一名用户，直接跳转到记录页面（用来做跑步运动用户搜索其他用户的运动记录的，由于运动记录接口没有按用户名搜索，只有按userid，只能在这里通过用户名搜索到用户再拿用户的userid去搜索运动记录）
        async getList(initLoad = false) {
            if (this.load_page === 1) {
                this.list = []
                this.is_last_page = false
                if (!this.init_load) this.init_load = true
                this.$uni.showLoading()
            } else {
                this.more_loading = true
            }

            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.admin/active_attend_user_list',
                data: {
                    active_id: this.active_id,
                    truename: this.search_keyword,
                    page: this.load_page,
                    perpage: 20
                }
            })

            const hideLoading = () => {
                if (this.load_page === 1) {
                    this.init_load = false
                    uni.hideLoading()
                } else {
                    this.more_loading = false
                }
            }

            if (!res?.status) {
                this.is_last_page = true
                hideLoading()
                return
            }

            const res_data = res.data.user_list
            const list = (res_data.data || []).map(item => ({
                id: item.id,
                userid: item.userid,
                nickname: item.must_submit?.[0]?.value || '',
                headimg: item.headimg || ''
            }))

            if (initLoad && list.length === 1) {
                this.lookRunRecord(list[0], true)
                return
            }

            this.list = [...this.list, ...list]
            this.is_last_page = res_data.is_lastpage

            hideLoading()
        },

        checkItem(item) {
            this.lookRunRecord(item, false)
        },

        // closePage 是否关闭当前页面，进入页面就搜索的话，如果搜索结果只有一名用户，直接跳转到记录页面并关闭当前页面（用来做跑步运动用户搜索其他用户的运动记录的，由于运动记录接口没有按用户名搜索，只有按userid，只能在这里通过用户名搜索到用户再拿用户的userid去搜索运动记录）
        lookRunRecord(item, closePage = false) {
            if (!item.userid) return this.$uni.showToast('没有获取到会员的会员号')
            const url = `/pages/running/user/run_list?id=${this.active_id}&nickname=${item.nickname}&userid=${item.userid}`
            closePage ? this.$uni.redirectTo(url) : this.$uni.navigateTo(url)
        }
    }
}
</script>

<style scoped lang="scss">
.page {
    min-height: 100vh;
    padding-top: 60px;
    padding-bottom: 100px;
    box-sizing: border-box;
}

.top-view {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    z-index: 999;
}

.search .input {
    width: calc(100% - 70px);
    border-radius: 18px;
}

.search-go {
    width: 60px;
    min-width: 60px;
    text-align: right;
    line-height: 36px;
}


/* #ifdef H5 */
.search-go {
    padding-right: 10px;
    box-sizing: border-box;
}

@media screen and (min-width: 500px) {
    .top-view {
        width: 500px;
        left: calc(((100vw - 500px) / 2) - 10px);
    }

    .search .input {
        width: 440px;
    }
}

/* #endif */


.item {
    margin: 10px;
    padding: 10px;
    border-radius: 10px;
}

.headimg {
    width: 50px;
    min-width: 50px;
    height: 50px;
    border-radius: 50%;
    display: block;
}

.middle {
    width: 100%;
    padding-left: 10px;
    overflow: hidden;
    box-sizing: border-box;
}
</style>
