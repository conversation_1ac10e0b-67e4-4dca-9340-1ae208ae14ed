<template>
    <view class="page bg-background">
        <view v-if="isPublic" class="top-view p10">
            
            <view class="flex-row top-switch-bar bg-white">
                <view class="switch-bar-item" :class="{'switch-bar-item-active': !myself}"
                      @click="isMyselfSwitch(false)">运动广场</view>
                <view class="switch-bar-item" :class="{'switch-bar-item-active': myself}"
                      @click="isMyselfSwitch(true)">我的记录</view>
            </view>

            <view v-if="myself && myCumulativeData" class="user-card flex-kai bg-white">
                <view class="flex-row">
                    <view v-if="myCumulativeData.headimg" class="user-headimg">
                        <image class="user-headimg-image" :src="myCumulativeData.headimg"
                               mode="aspectFill"/>
                    </view>
                    <view>
                        <view class="color-title">{{ myCumulativeData.nickname }}</view>
                        <view class="color-success">
                            <text class="font14 color-sub pr5">
                                累计运动{{ myCumulativeData.run_counts }}次
                            </text>
                            <text class="italic-text font-bold font20">{{ myCumulativeData.meter }}</text>
                            <text class="italic-text font-bold font14">km</text>
                            <text v-if="marathonCount" class="font14 color-sub pl10" 
                                  @click="showExplain('marathon')">{{ marathonCount }}
                            </text>
                        </view>
                    </view>
                </view>
                <view></view>
            </view>
        </view>

        <view v-if="showSearchBar" class="search-container">
            <view class="search bg-white flex-kai p10">
                <uni-easyinput
                    ref="searchInput"
                    class="input bg-background"
                    prefix-icon="search"
                    confirm-type="search"
                    @confirm="search()"
                    placeholder="输入用户名字搜索"
                    trim="all"
                    :input-border="false"
                />
                <view class="search-go color-info pl10" @click="search">搜索</view>
            </view>
        </view>

        
        <view v-if="isPublic" style="width: 100%; height: 70px;"></view>
        <view v-if="isPublic && myself && myCumulativeData" style="width: 100%; height: 75px;"></view>
        <view v-if="showSearchBar" style="width: 100%; height: 60px;"></view>
        <view style="width: 100%; height: 1px;"></view>

        <view v-if="!close_ad" class="m10 radius10">
            <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
        </view>
        
<!--        <view v-if="!in_load && list_count && myself" class="text-center font14 color-sub pt10">
            共{{ list_count }}条运动记录
        </view>-->
        
        <view>
            <view v-for="(item, index) in list" :key="item.id">
                <view class="item bg-white p10 radius10">
                    <run-data-card
                        :is-me="myself"
                        :headimg="item.headimg"
                        :nickname="item.nickname"
                        :total-text="item.totalText"
                        :meter="item.meter"
                        :seconds="item.seconds"
                        :pace="item.pace"
                        :calories="item.calories"
                        :create-time="item.create_time"
                        :show-map-icon="item.showMap"
                        @clickMapIcon="lookMap(index)"
                        :show-delete="isAdmin"
                        @deleteRecord="deleteRecord(item.id)"
                    />
                </view>

                <view v-if="!close_ad && ((index + 1) % 10 === 0)" class="m10 radius10">
                    <xwy-ad :activity_id="id" :ad_type="66"></xwy-ad>
                </view>
            </view>

        </view>

        <view v-if="!list.length && !in_load" class="text-center" style="padding-top: 10vh;">
            <text class="iconfont icon-empty-state color-border" style="font-size: 100px;"></text>
            <view class="color-sub">暂无运动记录</view>
        </view>

        <view v-if="in_load" :style="{paddingTop: load_page === 1 ? '30vh' : '0'}">
            <uni-load-more status="loading"/>
        </view>
        <uni-load-more
            v-if="is_lastpage && list.length >= 4"
            status="noMore"
            :contentText="{contentnomore: '没有更多了'}"
        />
        <uni-load-more v-if="!is_lastpage && !in_load" status="more"/>
        
        <view v-if="isPublic" class="bottom-button bg-white">
            <view class="to-run-button text-center" @click="toRun">
                <text class="iconfont icon-walk color-white"></text>
                <text class="color-white pl5">去运动</text>
            </view>
        </view>

    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import runDataCard from '../components/run-data-card.vue'

export default {
    components: {
        runDataCard
    },
    data() {
        return {
            id: '',
            active_types: 12,   // 活动类型 默认是12 运动轨迹活动
            in_load: true,
            list: [],
            load_page: 1,
            is_lastpage: false,
            list_count: 0,
            myself: false,
            close_ad: false,
            isAdmin: false,
            exchange_kilo_unit: '里',
            myCumulativeData: null,
            userid: 0
        }
    },
    
    watch: {
        myself(value) {
            if (!this.id) this.$uni.setNavigationBarTitle(value ? '我的运动记录' : '运动广场')
        }
    },
    
    computed: {
        isPublic() {
            return !this.id
        },

        marathonCount() {
            const meter = this.myCumulativeData?.meter || 0
            if (!meter) return 0
            const FullMarathon = 42.195, HalfMarathon = 21.0975, QuarterMarathon = 10.54825
            if (meter >= FullMarathon) return `≈${Math.floor(meter / FullMarathon)}次全马`
            if (meter >= HalfMarathon) return `≈${Math.floor(meter / HalfMarathon)}次半马`
            if (meter >= QuarterMarathon) return `≈${Math.floor(meter / QuarterMarathon)}次四分马`
            return 0
        },

        showSearchBar() {
            return !this.isPublic && !this.myself
        }
    },

    onLoad(e) {
        this.paramsHandle(e)

        login.uniLogin(err => {
            if (err?.errMsg) {
                this.in_load = false
                return this.$uni.showModal(err.errMsg, {
                    title: err['errTitle'], 
                    success: () => uni.navigateBack()
                })
            }

            this.getActivityData()
        })
    },

    onPullDownRefresh() {
        this.load_page = 1
        this.getList().finally(() => uni.stopPullDownRefresh())
    },

    onReachBottom() {
        !this.in_load && !this.is_lastpage && this.getList()
    },

    methods: {
        paramsHandle(e) {
            let navigationBarTitle = '运动广场'
            if (e.id) {
                this.id = e.id
                uni.hideShareMenu(undefined) // 活动里面的不能加分享
            } else {
                uni.setNavigationBarColor({
                    frontColor: '#ffffff',
                    backgroundColor: '#19be6b'
                })
            }
            if (e.close_ad && e.close_ad === 1) this.close_ad = true
            if (e.userid) {
                this.userid = Number(e.userid)
                if (this.userid === app.globalData['userid']) e.myself = 1
            }
            if (e.myself) {
                this.myself = true
                navigationBarTitle = '我的运动记录'
            }
            
            if (e.nickname) navigationBarTitle = `${e.nickname || ''} 运动记录`

            this.$uni.setNavigationBarTitle(navigationBarTitle)
        },
        
        async getActivityData() {
            if (this.isPublic) return await this.getList()
            
            let activity_detail = app.globalData['activity_detail']
            if (!activity_detail) {
                const res = await this.xwy_api.request({
                    url: 'front.flat.sport_step.active_list/active_details',
                    data: {active_id: this.id}
                })
                activity_detail = res?.['data']?.['active_details'] || {}
            }

            // OA开启了 zs+110 关闭排行榜点击头像查看别人记录  只能管理员和自己才能查看轨迹地图
            if (activity_detail.rank_set?.['closed_user_exchange_list']) {
                this.justMeOrAdminLook = true
                this.isAdminLook = activity_detail.userid === app.globalData['userid']
            }
            
            if (activity_detail?.types) this.active_types = activity_detail.types
            this.exchange_kilo_unit = activity_detail.conf.active.kilo_unit || '里'

            this.isAdmin = app.globalData.userid === activity_detail.userid

            await this.getList()
        },

        search() {
            const name = this.$refs.searchInput.val
            console.log(name);
            if (!name || !name.trim()) return this.$uni.showToast('请输入用户姓名')
            this.$uni.navigateTo(`/pages/running/user/user-search-user?active_id=${this.id}&name=${name}`)
        },

        isMyselfSwitch(myself) {
            if (this.myself === myself) return
            this.myself = myself
            this.load_page = 1
            this.getList()
        },
        

        metre2kilometre(metre) {
            return Math.floor(metre / 10) / 100
        },
        async getList() {
            this.in_load = true
            if (this.load_page === 1) {
                this.list = []
                this.is_lastpage = false
            }
            const data = {
                page: this.load_page,
                perpage: 20
            }
            if (this.id) data.active_id = this.id
            if (this.myself) data.only_myself = 1
            if (this.userid) data.userid = this.userid
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.running.user/running_list',
                data
            })

            this.in_load = false
            this.load_page++

            if (res?.data?.['running_list']?.data?.length) {
                let list = res.data['running_list'].data
                !this.myCumulativeData && this.getMyCumulativeData(list)
                list = this.filterList(list)

                this.is_lastpage = res.data['running_list'].is_lastpage
                this.list_count = res.data['running_list'].total
                this.list = [...this.list, ...list]
            } else {
                this.is_lastpage = true
            }
        },

        getMyCumulativeData(list) {
            if (!this.isPublic || !this.myself || !list.length) return
            const data = list[0]

            this.myCumulativeData =  {
                headimg: data.headimg,
                nickname: data.nickname,
                meter: this.metre2kilometre(data['user_total_data']?.meter || 0),
                run_counts: data['user_total_data']?.run_counts || 0
            }
        },
        

        // 过滤掉不需要的数据，避免页面数据过大
        filterList(list) {
            list.forEach(v => {
                let haveMap = false
                v.showMap = true
                
                if (v.user_running_data?.content?.running_map?.polyline?.length) {
                    haveMap = true
                    this.user_running_data_list = this.user_running_data_list || []
                    this.user_running_data_list.push({
                        id: v.id,
                        user_running_data: v.user_running_data
                    })
                    delete v.user_running_data
                }
                v.meter_total = v['user_total_data']?.meter || 0
                v.run_counts = v['user_total_data']?.run_counts || 0
                v['user_total_data'] && delete v['user_total_data']

                if (v.conf_json) {
                    // 健步走活动需要把"累计运动x次，x公里"改为"本次兑换里程 x${健步走活动里程单位}"
                    if (this.active_types && this.active_types === 2 && v.conf_json.exchange_step_kilo) {
                        v.exchange_step_kilo = v.conf_json.exchange_step_kilo
                    }
                    v.showMap = !v.conf_json.hide_map
                    if (v.conf_json.calories) v.calories = Number((v.conf_json.calories / 100).toFixed(2))
                    delete v.conf_json
                }
                
                
                if (this.isPublic && this.myself) {
                    // 公共的，自己的记录不用显示累计数据，因为顶部已经有了
                    v.totalText = ''
                } else {
                    if (this.active_types === 2) {
                        const kilo = Math.floor((v.exchange_step_kilo || 0) * 100) / 100
                        v.totalText = `本次兑换里程 ${kilo}${this.exchange_kilo_unit}`
                    } else {
                        v.totalText = `累计运动${v.run_counts || 0}次, ${this.metre2kilometre(v.meter_total || 0)}公里`
                    }
                }

                // OA开启了 zs+110 关闭排行榜点击头像查看别人记录  只能管理员和自己才能查看轨迹地图
                if (this.justMeOrAdminLook) {
                    v.showMap = this.isAdminLook || app.globalData['userid'] === v.userid
                }

                // 无论如何，管理员和自己都可以查看轨迹地图，没有记录到轨迹除外
                if (this.isAdmin || app.globalData.userid === v.userid) v.showMap = true
                if (!haveMap) v.showMap = false

                delete v.active_id
                delete v.userid
            })
            return list
        },


        lookMap(index) {
            const item = this.list[index]
            const {meter, pace, seconds, calories} = item
            const {running_map} = this.user_running_data_list.find(v => v.id === item.id).user_running_data.content
            const runRecordData = {running_map, meter, pace, seconds, calories}
            this.$uni.navigateTo('./run_map', {
                success: res => res.eventChannel.emit('runRecordData', runRecordData)
            })
        },

        deleteRecord(id) {
            uni.showModal({
                title: '提示',
                content: '确定删除该记录？',
                confirmText: '删除',
                success: res => {
                    res.confirm && this.deleteRecordAjax(id)
                }
            })
        },

        async deleteRecordAjax(id) {
            uni.showLoading({
                title: '删除中...'
            })
            const res = await this.xwy_api.deleteRecords(28, id)
            uni.hideLoading()
            if (!res?.status || res.status !== 1) {
                this.xwy_api.alert(res?.info || '删除失败')
                return
            }
            uni.showToast({
                title: '删除成功!',
                icon: 'success'
            })

            this.load_page = 1
            await this.getList()
        },
        

        toRun() {
            this.$uni.navigateTo('./run', {
                events: {
                    refreshList: () => {
                        this.load_page = 1
                        this.getList()
                    }
                }
            })
        },

        showExplain() {
            this._utils.showRunExplain('marathon')
        },
    },

    onShareAppMessage() {
        let path = 'pages/running/user/run_list'
        if (this.id) path += `?id=${this.id}`
        return {
            title: '一起运动吧',
            path
        }
    }
}
</script>

<style lang="scss">
.page {
    min-height: 100vh;
    padding-bottom: 100px;
    box-sizing: border-box;
}

.bottom-button, .top-view {
    position: fixed;
    z-index: 9999;
    width: 100vw;
}

.top-view {
    box-sizing: border-box;
    background: linear-gradient(to bottom, #19be6b, #bbf6d8);
}

.top-switch-bar {
    padding: 5px;
    border-radius: 25px;
    
    .switch-bar-item {
        width: 50%;
        line-height: 40px;
        text-align: center;
        color: #666;
        border-radius: 20px;
    }
    .switch-bar-item-active {
        background: linear-gradient(to right, #19be6b, #6fd2a0);
        color: #fff;
    }
    
}

.search-container {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 99;
    width: 100%;

    .search {
        .input {
            width: calc(100% - 70px);
            border-radius: 18px;
        }

        .search-go {
            width: 60px;
            min-width: 60px;
            text-align: right;
            line-height: 36px;
        }
    }
}

.user-card {
    margin-top: 10px;
    padding: 10px;
    box-sizing: border-box;
    border-radius: 10px;
    
    .user-headimg {
        padding-right: 10px;
        .user-headimg-image {
            width: 50px;
            min-width: 50px;
            height: 50px;
            border-radius: 50%;
        }
    }
}


.item {
    margin: 20px 10px;
}

.bottom-button {
    bottom: 0;
    left: 0;
    padding: 10px 10px 20px;
    box-sizing: border-box;
    
    .to-run-button {
        line-height: 44px;
        border-radius: 22px;
        background: linear-gradient(to right, #6fd2a0, #19be6b);
    }
}

/* #ifdef H5 */
@media screen and (min-width: 500px) {
    .bottom-button, .top-view, .search-container {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }

    .search-container {
        .search-go {
            padding-right: 10px;
            box-sizing: border-box;

            .search {
                .input {
                    width: 440px;
                }
            }
        }
    }
}
/* #endif */
</style>
