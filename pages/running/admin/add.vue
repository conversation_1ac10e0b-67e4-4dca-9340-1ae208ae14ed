<template>
    <view class="page">
        <view v-if="success" class="save-success bg-white">
            <view class="icon text-center">
                <uni-icons type="checkbox-filled" size="100" color="#19be6b"/>
                <view class="color-content font16">活动{{ form_data.active_id ? '修改' : '创建' }}成功！</view>
            </view>
            <view class="flex-all-center">
                <button class="bg-success color-white" style="width: 200px;" @click="lookMyActivityList">
                    查看我的活动
                </button>
            </view>
        </view>

        <view class="type-list flex-row bg-white">
            <view
                class="type-item color-content"
                v-for="(item, index) in type_list"
                :key="index"
                :class="{'active-type': item.id === type_id}"
                :style="{width: 'calc(100% / ' + type_list.length + ')'}"
                @click="type_id = item.id"
            >
                {{ item.title }}
            </view>
        </view>

        <add-activity-tips ref="add_activity_tips"/>

        <view class="form">
            <template v-if="type_id === 1">
                <view class="form-item">
                    <view class="top color-content">
                        <text>活动名称</text>
                        <text class="color-error font16"> *</text>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.name"
                               placeholder="请输入活动名称"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">主办方</view>
                    <view class="bottom font16">
                        <input class="input" type="text" v-model="form_data.organizer"
                               placeholder="请输入主办方单位名称"/>
                    </view>
                </view>

                <template v-if="!form_data.active_id">
                    <view class="form-item">
                        <view class="top color-content">
                            <text>手机号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="number" v-model="form_data.mobile"
                                   placeholder="请输入真实手机号"/>
                        </view>
                    </view>

                    <view class="form-item">
                        <view class="top color-content">
                            <text>微信号</text>
                            <text class="color-error font16"> *</text>
                        </view>
                        <view class="bottom font16">
                            <input class="input" type="text" v-model="form_data.wechat_num"
                                   placeholder="请输入真实微信号"/>
                        </view>
                    </view>
                </template>

                <view class="form-item">
                    <view class="top color-content">活动开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.begin_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">活动结束时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="form_data.end_time"
                                end="2038-12-31"
                                return-type="timestamp"
                                :border="false"
                                :clear-icon="false"
                            />
                        </view>
                    </view>
                </view>

                <view v-if="!conf.active.news.news_id" class="form-item">
                    <view class="top color-content">活动说明</view>
                    <view class="bottom font16">
				        <textarea
                            class="textarea"
                            maxlength="-1"
                            auto-height="true"
                            v-model="form_data.content"
                            placeholder="请输入活动说明"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>活动说明文章</view>
                        <view class="font12 color-sub">
                            绑定文章后，活动说明将会显示文章内容，不会显示上面填写的活动说明
                        </view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai" @click="toSelNews">
                            <view class="view">
                                <view v-if="conf.active.news.news_id">
                                    {{ conf.active.news.news_title || conf.active.news.news_id }}
                                </view>
                                <view v-else class="color-sub">选择文章</view>
                            </view>
                            <view class="flex-all-center">
                                <view
                                    v-if="conf.active.news.news_id"
                                    class="color-sub font12"
                                    style="width: 30px;"
                                    @click.stop="deleteNews"
                                >解绑
                                </view>
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>
            </template>


            <template v-if="type_id === 3">
                <view class="form-item">
                    <view class="top color-content">活动参与方式</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.enter_types_list"
                                :value="form_options.enter_types_list.findIndex(v => v.value === conf.active.enter_types)"
                                range-key="title"
                                @change="conf.active.enter_types = form_options.enter_types_list[$event.detail.value].value"
                            >
                                {{ form_options.enter_types_list.find(v => v.value === conf.active.enter_types).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <template v-if="rank_set.batch_import && conf.active.enter_types === 4">
                    <view class="form-item">
                        <view class="top color-content">参与活动账号输入提示</view>
                        <view class="bottom font16">
                            <input class="input" placeholder="默认为“请输入账号”"
                                   v-model="conf.active.batch_import_label.username"/>
                        </view>
                    </view>
                    <view class="form-item">
                        <view class="top color-content">参与活动密码输入提示</view>
                        <view class="bottom font16">
                            <input class="input" placeholder="默认为“请输入密码”"
                                   v-model="conf.active.batch_import_label.password"/>
                        </view>
                    </view>
                </template>

                <view v-if="conf.active.enter_types === 2" class="form-item">
                    <view class="top color-content">
                        <text>活动密码</text>
                        <text v-if="!have_password" class="color-error font16"> *</text>
                        <text class="color-sub font12 pl5">(3-20位)</text>
                    </view>
                    <view class="bottom font16">
                        <input
                            class="input"
                            type="text"
                            v-model="conf.active.password"
                            :maxlength="20"

                            :placeholder="have_password ? '已设置密码，无需修改密码不用填写' : '请输入活动密码'"
                        />
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">报名开始时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="conf.active.submit.begin"
                                end="2038-12-31"
                                :border="false"
                                :clear-icon="true"
                            />
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">报名截止时间</view>
                    <view class="bottom font16">
                        <view class="view" style="padding: 0;">
                            <uni-datetime-picker
                                type="datetime"
                                v-model="conf.active.submit.end"
                                end="2038-12-31"
                                :border="false"
                                :clear-icon="true"
                            />
                        </view>
                    </view>
                </view>

                <must-submit-set :must-submit.sync="conf.must_submit"/>


                <view v-if="rank_set && rank_set.team_group_open" class="form-item">
                    <view class="top color-content">参与活动是否必选队伍</view>
                    <view class="bottom font16">
                        <view class="flex-kai">
                            <picker
                                class="view"
                                mode="selector"
                                :range="['非必选', '必选']"
                                :value="conf.active.team_required"
                                @change="conf.active.team_required = Number($event.detail.value)"
                            >
                                {{ conf.active.team_required ? '' : '非' }}必选
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <template v-if="rank_set && rank_set['select_sex']">
                    <active-sex-set :sex-required.sync="conf.active.sex_required"
                                    :sex-label.sync="conf.active.sex_label"/>
                </template>

            </template>


            <view v-show="type_id === 6">
                <view class="form-item">
                    <view class="top color-content">每天运动时间限制</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="['关闭', '开启']" :value="conf.active.run_time_limit"
                                    @change="conf.active.run_time_limit = Number($event.detail.value)">
                                {{ conf.active.run_time_limit ? '开启' : '关闭' }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <run-time-list-set v-if="conf.active.run_time_limit" :list.sync="conf.active.run_time_list"/>


                <view class="form-item">
                    <view class="top color-content">
                        <view>是否保存未提交的运动记录</view>
                        <view class="color-sub font12">保存用户未提交的运动记录，下次运动前可提交。</view>
                    </view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker class="view" :range="['保存', '不保存']"
                                    :value="conf.active.not_save_run_storage"
                                    @change="conf.active.not_save_run_storage = Number($event.detail.value)">
                                {{ conf.active.not_save_run_storage ? '不' : '' }}保存
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>


                <view class="form-item">
                    <view class="top color-content">
                        <view>
                            <text>最低跑步里程</text>
                            <text class="color-sub font14 pl5">(单位: km)</text>
                        </view>
                        <view class="color-sub font12 pl5">低于设置里程无法提交跑步记录</view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" placeholder="不填或填0不限制"
                               v-model="conf.active.mileage_limit"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>
                            <text>跑步运动最低配速限制</text>
                            <text class="color-sub font14 pl5">(单位: 秒)</text>
                        </view>
                        <view class="color-sub font12">提交跑步运动记录时, 如配速低于设置配速, 无法提交
                            (正常人跑步，跑一公里所需要的时间为333秒，即5.5分钟)
                        </view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" placeholder="不填或填0不限制"
                               v-model="conf.active.pace_limit"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">
                        <view>
                            <text>跑步运动最高配速限制</text>
                            <text class="color-sub font14 pl5">(单位: 秒)</text>
                        </view>
                        <view class="color-sub font12">提交跑步运动记录时, 如配速高于设置配速, 无法提交</view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="number" placeholder="不填或填0不限制"
                               v-model="conf.active.pace_max_limit"/>
                    </view>
                </view>

                <view v-if="rank_set['diy_books']" class="form-item">
                    <view class="top color-content">
                        <view>
                            <text>证书目标里程</text>
                            <text class="color-sub font14 pl5">(单位: km)</text>
                        </view>
                        <view class="color-sub font12">用户累计跑步达到目标里程可解锁证书</view>
                    </view>
                    <view class="bottom font16">
                        <input class="input" type="digit" placeholder="不填或填0不限制"
                               v-model="conf.active.certificate_mileage"/>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">是否显示活动规则</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="['显示', '不显示']"
                                :value="conf.active.activity_rules_hide"
                                @change="conf.active.activity_rules_hide = Number($event.detail.value)"
                            >
                                <template v-if="conf.active.activity_rules_hide">不</template>
                                显示
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <view class="form-item">
                    <view class="top color-content">意见反馈</view>
                    <view class="bottom font16">
                        <view class="flex-kai">

                            <picker
                                class="view"
                                mode="selector"
                                :range="form_options.close_feedback_opt"
                                :value="form_options.close_feedback_opt.findIndex(v => v.value === conf.active.close_feedback)"
                                range-key="title"
                                @change="conf.active.close_feedback = form_options.close_feedback_opt[$event.detail.value].value"
                            >
                                {{ form_options.close_feedback_opt.find(v => v.value === conf.active.close_feedback).title }}
                            </picker>
                            <view class="flex-all-center">
                                <uni-icons type="forward" color="#80848f"/>
                            </view>
                        </view>
                    </view>
                </view>

                <active-share-set :active-id="form_data.active_id" :rank-set="rank_set"
                                  :qrcode-logo.sync="conf.active.qrcode_logo"
                                  :share-image.sync="conf.active.share_image"
                                  :share-title.sync="conf.active.share_title"/>
            </view>


            <template v-if="type_id === 2">
                <active-image-set :active-id="form_data.active_id" :rankSet="rank_set"
                                  :logo.sync="form_data.logo"
                                  :screen-pic.sync="conf.active.screen_pic"
                                  :top-rank-banner.sync="conf.active.top_rank_banner"/>
            </template>

            <template v-if="type_id === 6">


            </template>
        </view>

        <view class="bottom-btn-view bg-white flex-all-center">
            <view
                class="login-btn color-white text-center font18 bg-primary"
                :disabled="loading"
                @click="save"
            >{{ form_data.active_id ? '保存' : '创建活动' }}
            </view>
        </view>
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'
import utils from '@/utils/utils.js'
import base64 from '@/utils/base64.js'
import {pinyin} from 'pinyin-pro'
import runTimeListSet from '../components/run-time-list-set.vue'


export default {
    components: {runTimeListSet},

    data() {
        return {
            loading: true,
            success: false,
            type_list: [
                {title: '基本信息', id: 1},
                {title: '活动设置', id: 3},
                {title: '扩展设置', id: 6},
                {title: '活动图片', id: 2}
            ],
            type_id: 1,
            form_data: {
                name: '',
                wechat_num: '',
                mobile: '',
                organizer: '',
                content: '',
                begin_time: new Date(utils.getDay(0, true, '/') + ' 00:00:00').getTime(),
                end_time: new Date(utils.getDay(30, true, '/') + ' 23:59:59').getTime(),
                logo: ''
            },
            conf: {
                active: {
                    news: {
                        news_id: '',
                        news_title: ''
                    },
                    enter_types: 1,
                    password: '',
                    screen_pic: '',
                    top_rank_banner: [],
                    submit: {
                        begin: '',
                        end: ''
                    },
                    close_feedback: 0,
                    pace_limit: '',
                    pace_max_limit: '',
                    mileage_limit: '',
                    share_title: '',
                    share_image: '',
                    qrcode_logo: '',
                    sex_required: 0,
                    sex_label: {
                        man: '',
                        woman: ''
                    },
                    team_required: 0,
                    certificate_mileage: '',
                    activity_rules_hide: 0,
                    run_time_limit: 0,
                    run_time_list: [],
                    not_save_run_storage: 0,

                    batch_import_label: {
                        username: '请输入账号',
                        password: '请输入密码'
                    },
                },
                must_submit: JSON.parse(JSON.stringify(this.xwy_config.defaultMustSubmit))
            },
            top_rank_banner_max_count: 6,
            rank_set: {},
            have_password: false,
            form_options: {
                enter_types_list: [
                    {title: '自由报名参与活动', value: 1},
                    {title: '需要输入密码才能进入活动', value: 2},
                    {title: '报名需要审核通过才能参与活动', value: 3}
                ],
                close_feedback_opt: [
                    {value: 0, title: '开启意见反馈'},
                    {value: 1, title: '关闭意见反馈'}
                ]
            },
            detail_icon_conf: [
                {
                    type: 0,
                    title: '文章详情'
                }
            ]
        }
    },
    onLoad(e) {
        if (e.id) {
            this.form_data.active_id = e.id
        } else {
            this.types = Number(e.types)
            this.$nextTick(() => this.$refs.add_activity_tips.open(this.types))
            this.form_data.logo = this.xwy_config.create_active_default_logo
        }
        uni.setNavigationBarTitle({
            title: e.id ? '修改活动' : `创建${e.name || '活动'}`
        })
        uni.showLoading({
            mask: true
        })


        login.uniLogin(err => {

            if (err && err.errMsg) {
                uni.hideLoading()
                uni.showModal({
                    title: err.errTitle || '提示',
                    content: err.errMsg,
                    showCancel: false
                })
                return false
            }


            if (e.id) {
                this.getDetail()
                return false
            }

            const evn_version = app.globalData.evn_version

            if (evn_version === 'develop') {
                this.form_data.mobile = 12345678910
                this.form_data.wechat_num = 12345678910
                this.form_data.organizer = '伟杰测试'
            }

            // 开发版和体验版不检查重复创建活动
            if (evn_version === 'develop' || evn_version === 'trial') {
                uni.hideLoading()
                return false
            }

            this.checkUserCanCreateActive()
        })
    },
    methods: {
        checkUserCanCreateActive() {

            xwy_api.ajax({
                url: 'front.flat.sport_step.admin/check_user_can_create_active',
                data: {
                    access_token: app.globalData.access_token
                },
                success: res => {
                    console.log('检查是否能创建活动', res)
                    uni.hideLoading()
                    this.loading = false
                    if (!res.status) {
                        uni.showModal({
                            title: '提示',
                            content: res.info || '暂时不能创建活动',
                            showCancel: false,
                            success: () => {
                                uni.navigateBack()
                            }
                        })
                    }
                }
            })

        },

        getDetail() {
            const errModal = content => {
                uni.hideLoading()
                uni.showModal({
                    title: '提示',
                    content: content || '活动获取失败',
                    showCancel: false,
                    success: () => uni.navigateBack()
                })
            }

            xwy_api.getActivityDetail(this.form_data.active_id, res => {
                if (res.data?.active_details) {
                    const detail = res.data.active_details

                    this.detailInit(detail)
                } else {
                    errModal(res.info)
                }
            })
        },


        detailInit(data) {
            this.types = data.types
            this.form_data.name = data.name
            this.form_data.begin_time = data.begin_time * 1000
            this.form_data.end_time = data.end_time * 1000
            if (data.organizer) this.form_data.organizer = data.organizer
            if (data.logo) this.form_data.logo = data.logo
            if (data.content) this.form_data.content = data.content

            const conf = data.conf


            if (conf.active.password) {
                this.old_password = conf.active.password
                delete conf.active.password
                this.have_password = true
            }
            if (!conf.active.news) conf.active.news = this.conf.active.news
            if (!conf.active.screen_pic) conf.active.screen_pic = ''
            if (!conf.active.top_rank_banner) conf.active.top_rank_banner = []
            const submit = {
                begin: '',
                end: ''
            }
            if (!conf.active.submit) conf.active.submit = submit
            conf.active.submit.begin = conf.active.submit.begin || ''
            conf.active.submit.end = conf.active.submit.end || ''
            conf.active.close_feedback = conf.active.close_feedback || 0
            conf.active.pace_limit ||= ''
            conf.active.pace_max_limit ||= ''
            conf.active.mileage_limit ||= ''

            conf.active.share_title ||= ''
            conf.active.share_image ||= ''
            conf.active.qrcode_logo ||= ''
            conf.active.sex_required ||= 0
            if (conf.active.sex_required === 2) conf.active.sex_required = 1
            conf.active.sex_label ||= {man: '', woman: ''}
            conf.active.team_required ||= 0
            conf.active.certificate_mileage ||= ''
            conf.active.activity_rules_hide ||= 0
            conf.active.run_time_limit ||= 0
            conf.active.run_time_list ||= []
            conf.active.not_save_run_storage ||= 0
            conf.active.batch_import_label ||= {
                username: '请输入账号',
                password: '请输入密码'
            }

            this.conf = conf

            if (data.rank_set) {
                const rank_set = data.rank_set

                this.rank_set = rank_set


                if (rank_set.closed_AD && rank_set.closed_AD === 1) {
                    this.detail_icon_conf.push({
                        type: 1,
                        title: '文章列表'
                    })
                }

                if (rank_set.batch_import) {
                    this.form_options.enter_types_list.push({
                        value: 4,
                        title: '只能使用管理员提前导入的名单报名'
                    })
                }
            }

            if (!this.rank_set.batch_import && this.conf.active.enter_types === 4) {
                this.conf.active.enter_types = 1
            }

            uni.hideLoading()
        },


        toSelNews() {
            uni.navigateTo({
                url: '/pages/news/list?type=user&is_sel=true',
                events: {
                    selNews: data => {
                        this.conf.active.news.news_id = data.id
                        this.conf.active.news.news_title = data.title
                    }
                }
            })
        },


        deleteNews() {
            this.conf.active.news.news_id = ''
            this.conf.active.news.news_title = ''
        },


        dataCheck(data) {
            const showToast = title => {
                this.type_id = 1
                uni.showToast({
                    title,
                    icon: title.length <= 7 ? 'error' : 'none'
                })
            }

            if (!data.name) {
                showToast('请输入活动名称')
                return false
            }

            if (!data.active_id) {
                if (!data.mobile) {
                    showToast('请输入手机号')
                    return false
                }
                if (data.mobile.toString().length !== 11) {
                    showToast('手机号长度有误')
                    return false
                }
                if (!data.wechat_num) {
                    showToast('请输入微信号')
                    return false
                }
                const wechat_num = data.wechat_num.toString()
                if (wechat_num.length < 4 || wechat_num.length > 20) {
                    showToast('微信号长度有误')
                    return false
                }
                if (this._utils.isChineseChar(wechat_num)) {
                    showToast('微信号不能输入中文')
                    return false
                }
            }


            return true
        },

        setMustSubmitData() {
            const errModal = content => {
                this.type_id = 3
                uni.showModal({
                    title: '提示',
                    content,
                    showCancel: false
                })
            }

            for (let i = 0; i < this.conf.must_submit.length; i++) {
                const v = this.conf.must_submit[i]
                if (!v.title) return errModal('参与活动需要填写的信息选项填写不完整，请检查。')

                v.name = pinyin(v.title, {toneType: 'none', type: 'array'}).join('_')
                if (v.types === 2) {
                    if (!v.options || !v.options.length) return errModal(`${v.title} 至少需要添加一个选项。`)

                    for (let j = 0; j < v.options.length; j++) {
                        const v1 = v.options[j]
                        if (!v1 || !v1.text) return errModal(`${v.title} 有未填写的选项，请检查。`)
                    }
                }
            }

            return true
        },


        confCheck() {
            const showToast = title => {
                uni.showToast({
                    title,
                    icon: title.length <= 7 ? 'error' : 'none'
                })
            }
            const conf = JSON.parse(JSON.stringify(this.conf))
            if (conf.active.enter_types === 2) {
                if (!this.have_password && !conf.active.password) {
                    showToast('请输入活动密码')
                    this.type_id = 3
                    return false
                }
                if (conf.active.password && conf.active.password.length < 3) {
                    showToast('活动密码不得少于3位')
                    this.type_id = 3
                    return false
                }
                if (conf.active.password && conf.active.password.length > 20) {
                    showToast('活动密码不得大于20位')
                    this.type_id = 3
                    return false
                }
                conf.active.password = conf.active.password || this.old_password
            } else {
                conf.active.password = 1
            }

            if (!conf.active.news.news_id) delete conf.active.news
            if (!conf.active.screen_pic) delete conf.active.screen_pic
            if (!conf.active.top_rank_banner.length) delete conf.active.top_rank_banner
            if (!conf.active.submit.begin && !conf.active.submit.end) {
                delete conf.active.submit
            } else {
                if (conf.active.submit.begin.length < 15) conf.active.submit.begin += ' 00:00:00'
                if (conf.active.submit.end.length < 15) conf.active.submit.end += ' 23:59:59'
            }

            if (!conf.active.share_title) delete conf.active.share_title
            if (!conf.active.share_image) delete conf.active.share_image
            if (!conf.active.qrcode_logo || !this.rank_set?.closed_AD) delete conf.active.qrcode_logo

            if (this.rank_set?.['select_sex']) {
                if (!conf.active.sex_required) delete conf.active.sex_required
                if (!conf.active.sex_label.man && !conf.active.sex_label.woman) {
                    delete conf.active.sex_label
                }
            } else {
                delete conf.active.sex_required
                delete conf.active.sex_label
            }

            if (!conf.active.team_required || !this.rank_set?.team_group_open) delete conf.active.team_required

            if (this.rank_set?.['diy_books'] && conf.active.certificate_mileage) {
                const mileage = Number(conf.active.certificate_mileage)
                if (isNaN(mileage) || mileage < 0) {
                    showToast('证书目标里程请输入大于0的数字')
                    this.type_id = 6
                    return false
                }
                conf.active.certificate_mileage = mileage
            } else {
                delete conf.active.certificate_mileage
            }

            if (!conf.active.activity_rules_hide) delete conf.active.activity_rules_hide

            if (!conf.active.run_time_limit) delete conf.active.run_time_limit
            if (!conf.active.run_time_list.length) delete conf.active.run_time_list

            if (!conf.active.not_save_run_storage) delete conf.active.not_save_run_storage

            if (this.rank_set.batch_import) {
                conf.active.batch_import_label.username ||= '请输入账号'
                conf.active.batch_import_label.password ||= '请输入密码'
            } else {
                delete conf.active.batch_import_label
            }

            let conf_str = JSON.stringify(conf)
            conf_str = conf_str.replace(/·/g, '-')
            return base64.encode(conf_str)
        },

        save() {
            if (this.success) return false

            const data = JSON.parse(JSON.stringify(this.form_data))

            if (!this.dataCheck(data)) return false
            if (!this.setMustSubmitData()) return false

            const conf = this.confCheck()
            if (!conf) return false
            data.conf = conf


            data.begin_time /= 1000
            data.end_time /= 1000

            this.saveAjax(data)
        },

        async saveAjax(data) {
            data.access_token = app.globalData.access_token
            data.types = this.types

            this.loading = true
            uni.showLoading({
                title: '保存中...',
                mask: app.globalData.evn_version === 'trial' ? false : true
            })

            const res = await xwy_api.request({
                url: 'front.flat.sport_step.admin/create_active',
                data
            })

            uni.hideLoading()
            this.loading = false

            if (!res || !res.status) {
                xwy_api.alert(res && res.info || '保存失败')
                return false
            }

            this.updatePageData()

            if (data.active_id) {
                this.$uni.showToast('保存成功', 'success')
                return this.$uni.navigateBack(1, {delay: 1000})
            }

            this.success = true
        },

        // 活动修改/创建成功，如果页面栈有详情或列表页面，更新页面数据
        updatePageData() {
            const pages = getCurrentPages()

            const update_page_list = [
                {
                    route: 'pages/running/user/detail',
                    _function: page => {
                        page.$vm.getDetail(true)
                    }
                },
                {
                    route: 'pages/running/admin/manage',
                    _function: page => {
                        page.$vm.getDetail()
                    }
                },
                {
                    route: 'pages/activity/user/index',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getActivityList()
                    }
                },
                {
                    route: 'pages/activity/user/activity_list',
                    _function: page => {
                        page.$vm.load_page = 1
                        page.$vm.getList()
                    }
                }
            ]

            update_page_list.forEach(v => {
                const page = pages.find(n => n.route === v.route)
                if (page) v._function(page)
            })
        },

        lookMyActivityList() {
            const list_page_route = 'pages/activity/user/activity_list'
            const pages = getCurrentPages()
            const list_page_index = pages.findIndex(v => {
                return v.route === list_page_route && v.options.type && v.options.type === 'create'
            })
            if (list_page_index === -1) {
                uni.redirectTo({
                    url: `/${list_page_route}?type=create`
                })
                return false
            }

            const back_delta = pages.length - list_page_index - 1
            uni.navigateBack({
                delta: back_delta
            })
        }
    }
}
</script>

<style>
.page {
    padding-top: 40px;
    padding-bottom: 75px;
}

.type-list {
    position: fixed;
    z-index: 9;
    width: 100vw;
    top: 0;
    left: 0;
}

.type-item {
    height: 40px;
    line-height: 40px;
    text-align: center;
    box-sizing: border-box;
}

.active-type {
    color: #007AFF;
    border-bottom: 2px solid #007AFF;
}

.form {
    padding: 10px 0;
}

.form-item {
    padding: 10px;
}


.form-item .bottom {
    border-bottom: 1px solid #eee;
}


.form-item .textarea {
    width: 100%;
    line-height: 16px;
    padding: 10px 0;
}

.form-item .input {
    width: 100%;
    line-height: 40px;
    height: 40px;
}

.form-item .view {
    padding: 8px 0;
    width: 100%;
}


.bottom-btn-view {
    position: fixed;
    z-index: 9;
    bottom: 0;
    left: 0;
    width: 100vw;
    padding: 15px 0;
}

.login-btn {
    width: 250px;
    height: 44px;
    line-height: 44px;
    border-radius: 22px;
}

.save-success {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 999999;
    box-sizing: border-box;
    padding-top: 10vh;
}

.save-success .icon {
    padding-bottom: 50px;
}

.uni-date-x {
    padding: 0 !important;
}

.uni-date__x-input {
    font-size: 16px !important;
    color: #000;
}

/* #ifdef H5 */

@media screen and (min-width: 500px) {
    .type-list {
        width: 500px;
        left: calc((100vw - 500px) / 2);
    }
}

/* #endif */
</style>
