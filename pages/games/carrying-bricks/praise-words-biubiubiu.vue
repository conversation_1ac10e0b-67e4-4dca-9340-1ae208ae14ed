<template>
    <!-- 赞美词弹幕容器 -->
    <view class="praise-barrage-container">
        <view
            v-for="barrage in activeBarrages"
            :key="barrage.id"
            class="praise-barrage"
            :class="{ 'barrage-animate': barrage.animate }"
        >
            {{ barrage.text }}
        </view>
    </view>
</template>

<script>
import config from './config.js'

export default {
    name: 'PraiseWordsBiubiubiu',
    props: {
        // 接收距离参数
        distance: {
            type: Number,
            default: 0
        }
    },
    data() {
        return {
            // 弹幕相关数据
            activeBarrages: [], // 当前活跃的弹幕列表
            triggeredMilestones: new Set(), // 已触发的里程碑集合，防止重复触发
            barrageIdCounter: 0 // 弹幕ID计数器
        }
    },
    watch: {
        // 监听距离变化，触发里程碑检查
        distance: {
            handler(newDistance) {
                if (newDistance > 0) {
                    this.checkMilestoneBarrage(newDistance)
                }
            },
            immediate: true
        }
    },
    methods: {
        // 检查里程碑弹幕触发
        checkMilestoneBarrage(currentDistance) {
            // 检查配置中的里程碑
            const milestone = config.praiseWords.find(item =>
                item.min === currentDistance && !this.triggeredMilestones.has(item.min)
            )

            if (milestone) {
                this.showBarrage(milestone.title)
                this.triggeredMilestones.add(milestone.min)
                return
            }

            // 检查超过100米后的动态里程碑（每50米一次）
            if (currentDistance > 100 && (currentDistance - 100) % 50 === 0) {
                const dynamicMilestone = `dynamic_${currentDistance}`
                if (!this.triggeredMilestones.has(dynamicMilestone)) {
                    const dynamicPraise = this.generateDynamicPraise(currentDistance)
                    this.showBarrage(dynamicPraise)
                    this.triggeredMilestones.add(dynamicMilestone)
                }
            }
        },

        // 生成动态赞美词
        generateDynamicPraise(distance) {
            const praises = [
                `🚀 ${distance}米！你已经超越极限！`,
                `⭐ ${distance}米达成！无人能挡！`,
                `🔥 ${distance}米突破！传奇表现！`,
                `💎 ${distance}米里程碑！完美节奏！`,
                `🏆 ${distance}米征服！王者风范！`,
                `⚡ ${distance}米飞跃！速度之神！`
            ]
            // 根据距离选择不同的赞美词
            const index = Math.floor((distance - 150) / 50) % praises.length
            return praises[index]
        },

        // 显示弹幕
        showBarrage(text) {
            const barrageId = ++this.barrageIdCounter
            const barrage = {
                id: barrageId,
                text: text,
                animate: false
            }

            this.activeBarrages.push(barrage)

            // 下一帧开始动画
            this.$nextTick(() => {
                const barrageIndex = this.activeBarrages.findIndex(b => b.id === barrageId)
                if (barrageIndex !== -1) {
                    this.activeBarrages[barrageIndex].animate = true
                }
            })

            // 3秒后移除弹幕
            setTimeout(() => {
                const index = this.activeBarrages.findIndex(b => b.id === barrageId)
                if (index !== -1) {
                    this.activeBarrages.splice(index, 1)
                }
            }, 3000)
        },

        // 重置弹幕数据（供外部调用）
        resetBarrageData() {
            this.activeBarrages = []
            this.triggeredMilestones.clear()
            this.barrageIdCounter = 0
        }
    }
}
</script>

<style scoped lang="scss">
// 基础变量
$white: #fff;

// 混合器
@mixin center-flex {
    display: flex;
    justify-content: center;
    align-items: center;
}

// 赞美词弹幕样式
.praise-barrage-container {
    position: absolute;
    top: 100px; // Y轴位置距离屏幕顶部150px
    left: 0;
    width: 100%;
    z-index: 15; // 确保在最上层，不被其他元素遮挡
    pointer-events: none; // 不阻挡用户操作
}

.praise-barrage {
    position: absolute;
    right: -100%; // 初始位置在屏幕右侧外
    height: 32px; // 高度32px
    background: rgba(0, 0, 0, 0.6); // 半透明黑色背景
    color: $white; // 白色字体
    border-radius: 16px; // 圆角
    padding: 0 16px; // 内边距确保文字居中
    font-size: 14px; // 字体大小
    font-weight: bold; // 字体加粗
    white-space: nowrap; // 不换行
    @include center-flex; // 文字垂直居中
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3); // 添加阴影效果

    // 弹幕动画：从右侧进入向左移动
    &.barrage-animate {
        animation: barrage-move 3s linear forwards;
    }
}

// 弹幕移动动画
@keyframes barrage-move {
    0% {
        right: -100%; // 从屏幕右侧外开始
        opacity: 0;
    }
    10% {
        opacity: 1; // 快速显示
    }
    90% {
        opacity: 1; // 保持显示
    }
    100% {
        right: 100%; // 移动到屏幕左侧外
        opacity: 0; // 淡出
    }
}
</style>