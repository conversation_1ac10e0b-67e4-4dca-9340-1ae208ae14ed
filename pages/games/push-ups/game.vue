
<template>
    <view class="content relative" :style="{ 'background-image': `url(${images.bgImg})` }">

        <!-- 弹幕组件 -->
        <biubiubiu v-if="gameStatus" ref="danmakuComponent" />

        <view class="game-tips-msg-bar">
            <game-top-msg-bar>
                <template v-slot:leftMsg>{{ countdown }}</template>
                <template v-slot:rightMsg>{{ pushUpScore }}</template>
            </game-top-msg-bar>
        </view>

        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD"
                         @startGame="gameTipsClose"/>

        <ready-countdown ref="readyCountdown" @countdownOver="countdownOver"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>

        <!-- 游戏区域 -->
        <view class="game-area width100 f-d-c-j-a-c">
            <!-- 俯卧撑运动 -->
            <view class="inflator f">
                <image
                    :style="{ opacity: !pushUpsStatus ? 1 : 0 }"
                    :src="images.inflatorImg"
                    mode="widthFix"
                ></image>
                <image
                    :style="{ opacity: pushUpsStatus ? 1 : 0 }"
                    :src="images.inflator1Img"
                    mode="widthFix"
                ></image>
            </view>
        </view>

        <view v-if="gameStatus === 1" class="f-j-a-c width100 pt2">
            <view
                class="f push-button relative width50"
                :class="{ active: isButtonClicked }"
                @click.stop="pushUpsClick"
            >
                <image :src="images.pushBtnImg" mode="widthFix"/>
                <view
                    class="absolute colorfff font1_5 width100 text-center"
                    style="top: 50%; transform: translateY(-50%); letter-spacing: 2px"
                >
                    运动一下
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import config from './config'

import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import readyCountdown from '../components/ready-countdown.vue'
import biubiubiu from './biubiubiu.vue'

export default {
    components: {gameTipsPopup, gameTopMsgBar, readyCountdown, biubiubiu},
    data() {
        return {
            images: config.images,
            defaultScore: 1, // 默认每次得分分数

            gameStatus: 0, // 0: 未开始, 1: 游戏中, 2: 已结束
            pushUpScore: 0, // 俯卧撑数量
            pushUpsStatus: false,
            isButtonClicked: false, // 按钮点击状态

            per_integral: 0,
            unit: '积分',
            seconds: 30,
            countdown: 30,
            score: 0,
            showAD: false
        }
    },

    computed: {
        tipsList() {
            return [
                `请在${this.seconds}秒内点击屏幕中的按钮，每次点击做一个俯卧撑。`,
                `倒计时结束后，游戏结束，每做一个俯卧撑奖励${this.per_integral}${this.unit}。`
            ]
        }
    },

    onLoad() {
        this.init()
    },

    methods: {
        async init() {
            this.$refs.gameTipsPopup.open()
        },

        gameTipsClose() {
            this.$refs.gameTipsPopup.close()
            this.$refs.readyCountdown.open()
        },

        countdownOver() {
            this.startGame()
        },

        // 开始游戏
        startGame() {
            this.gameStatus = 1
            this.countdown = this.seconds
            this.pushUpScore = 0
            this.pushUpsStatus = false
            // 清空弹幕组件中的弹幕
            if (this.$refs.danmakuComponent) {
                this.$refs.danmakuComponent.clearDanmaku()
            }
            this.startTimer()
        },

        // 开始计时
        startTimer() {
            this.timer = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) this.endGame()
            }, 1000)
        },


        // 结束游戏
        endGame() {
            this.gameStatus = 2
            if (this.timer) {
                clearInterval(this.timer)
                this.timer = null
            }
        },

        // 运动点击
        pushUpsClick() {
            if (this.gameStatus !== 1) return

            // 触发点击动画
            this.isButtonClicked = true

            // 调用弹幕组件添加弹幕
            if (this.$refs.danmakuComponent) {
                this.$refs.danmakuComponent.addDanmaku()
            }

            setTimeout(() => (this.isButtonClicked = false), 200)

            this.pushUpsStatus = !this.pushUpsStatus
            this.pushUpScore += this.defaultScore
        }
    },

    onUnload() {
        if (this.timer) clearInterval(this.timer)
    },
}
</script>

<style scoped lang="scss">
.width100 {
  width: 100%;
}

.width50 {
  width: 50% !important;
}

.f {
    display: flex;
}

.f-d-c-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.f-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
}

.pt2 {
  padding-top: 2rem !important;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.colorfff {
  color: #fff !important;
}

.font1_5 {
	font-size: 1.5rem !important;
}



.content {
    width: 100%;
    min-height: 100vh;
    background-size: cover;
    background-repeat: no-repeat;
}

.game-area {
    padding-top: 72%;

    .inflator {
        position: relative;
        width: 80vw;
        max-width: 512px;
        height: 60vw;
        max-height: 384px;

        > image {
            position: absolute;
            left: 0;
            top: 0;
            width: 80vw;
            max-width: 512px;
            height: 60vw;
            max-height: 384px;
        }
    }
}

.push-button {
    transition: all 0.05s;

    &:active {
        transform: scale(0.9);
    }
}
</style>
