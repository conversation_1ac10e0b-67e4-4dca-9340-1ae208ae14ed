
<template>
    <view class="game-content game-content--relative" :style="{ 'background-image': `url(${images.bgImg})` }">

        <!-- 弹幕组件 -->
        <biubiubiu v-if="gameStatus" ref="danmakuComponent" />

        <view class="game-tips-msg-bar">
            <game-top-msg-bar>
                <template v-slot:leftMsg>{{ countdown }}</template>
                <template v-slot:rightMsg>{{ pushUpScore }}</template>
            </game-top-msg-bar>
        </view>

        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD"
                         @startGame="gameTipsClose"/>

        <ready-countdown ref="readyCountdown" @countdownOver="countdownOver"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>

        <!-- 游戏区域 -->
        <view class="game-area game-area--full-width game-area--flex-column">
            <!-- 俯卧撑运动 -->
            <view class="exercise-inflator exercise-inflator--flex">
                <image
                    :style="{ opacity: !pushUpsStatus ? 1 : 0 }"
                    :src="images.inflatorImg"
                    mode="widthFix"
                ></image>
                <image
                    :style="{ opacity: pushUpsStatus ? 1 : 0 }"
                    :src="images.inflator1Img"
                    mode="widthFix"
                ></image>
            </view>
        </view>

        <view v-if="gameStatus === 1" class="button-container button-container--flex-center button-container--full-width button-container--top-spacing">
            <view
                class="push-button push-button--flex push-button--relative push-button--half-width"
                :class="{ active: isButtonClicked }"
                @click.stop="pushUpsClick"
            >
                <image :src="images.pushBtnImg" mode="widthFix"/>
                <view
                    class="button-text button-text--absolute button-text--white button-text--large button-text--full-width button-text--center"
                    style="top: 50%; transform: translateY(-50%); letter-spacing: 2px"
                >
                    运动一下
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import config from './config'

import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import readyCountdown from '../components/ready-countdown.vue'
import biubiubiu from './biubiubiu.vue'

export default {
    components: {gameTipsPopup, gameTopMsgBar, readyCountdown, biubiubiu},
    data() {
        return {
            images: config.images,
            defaultScore: 1, // 默认每次得分分数

            gameStatus: 0, // 0: 未开始, 1: 游戏中, 2: 已结束
            pushUpScore: 0, // 俯卧撑数量
            pushUpsStatus: false,
            isButtonClicked: false, // 按钮点击状态

            per_integral: 0,
            unit: '积分',
            seconds: 30,
            countdown: 30,
            score: 0,
            showAD: false
        }
    },

    computed: {
        tipsList() {
            return [
                `请在${this.seconds}秒内点击屏幕中的按钮，每次点击做一个俯卧撑。`,
                `倒计时结束后，游戏结束，每做一个俯卧撑奖励${this.per_integral}${this.unit}。`
            ]
        }
    },

    onLoad() {
        this.init()
    },

    methods: {
        async init() {
            this.$refs.gameTipsPopup.open()
        },

        gameTipsClose() {
            this.$refs.gameTipsPopup.close()
            this.$refs.readyCountdown.open()
        },

        countdownOver() {
            this.startGame()
        },

        // 开始游戏
        startGame() {
            this.gameStatus = 1
            this.countdown = this.seconds
            this.pushUpScore = 0
            this.pushUpsStatus = false
            // 清空弹幕组件中的弹幕
            if (this.$refs.danmakuComponent) {
                this.$refs.danmakuComponent.clearDanmaku()
            }
            this.startTimer()
        },

        // 开始计时
        startTimer() {
            this.timer = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) this.endGame()
            }, 1000)
        },


        // 结束游戏
        endGame() {
            this.gameStatus = 2
            if (this.timer) {
                clearInterval(this.timer)
                this.timer = null
            }
        },

        // 运动点击
        pushUpsClick() {
            if (this.gameStatus !== 1) return

            // 触发点击动画
            this.isButtonClicked = true

            // 调用弹幕组件添加弹幕
            if (this.$refs.danmakuComponent) {
                this.$refs.danmakuComponent.addDanmaku()
            }

            setTimeout(() => (this.isButtonClicked = false), 200)

            this.pushUpsStatus = !this.pushUpsStatus
            this.pushUpScore += this.defaultScore
        }
    },

    onUnload() {
        if (this.timer) clearInterval(this.timer)
    },
}
</script>

<style scoped lang="scss">
/* ===== 通用布局工具类 ===== */
// 宽度相关
.game-area--full-width,
.button-container--full-width,
.button-text--full-width {
    width: 100%;
}

.push-button--half-width {
    width: 50%;
}

// Flexbox 布局
.exercise-inflator--flex {
    display: flex;
}

.game-area--flex-column {
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.button-container--flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.push-button--flex {
    display: flex;
}

// 间距相关
.button-container--top-spacing {
    padding-top: 2rem;
}

// 定位相关
.game-content--relative,
.push-button--relative {
    position: relative;
}

.button-text--absolute {
    position: absolute;
}

// 文本样式
.button-text--white {
    color: #fff;
}

.button-text--large {
    font-size: 1.5rem;
}

.button-text--center {
    text-align: center;
}



/* ===== 主要组件样式 ===== */
// 游戏主容器
.game-content {
    /* 布局属性 */
    width: 100%;
    min-height: 100vh;

    /* 背景属性 */
    background-size: cover;
    background-repeat: no-repeat;
}

// 游戏区域
.game-area {
    /* 布局属性 */
    padding-top: 72%;
}

// 运动器械容器
.exercise-inflator {
    /* 定位属性 */
    position: relative;

    /* 尺寸属性 */
    width: 80vw;
    max-width: 512px;
    height: 60vw;
    max-height: 384px;

    // 器械图片
    > image {
        /* 定位属性 */
        position: absolute;
        top: 0;
        left: 0;

        /* 尺寸属性 */
        width: 80vw;
        max-width: 512px;
        height: 60vw;
        max-height: 384px;
    }
}

// 按钮容器
.button-container {
    /* 这里可以添加按钮容器特有的样式 */
}

// 运动按钮
.push-button {
    /* 动画效果 */
    transition: all 0.05s;

    &:active {
        transform: scale(0.9);
    }
}

// 按钮文本
.button-text {
    /* 这里可以添加按钮文本特有的样式 */
}
</style>
