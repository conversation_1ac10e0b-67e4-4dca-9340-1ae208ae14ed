
<template>
    <view class="content relative" :style="{ 'background-image': `url(${images.bgImg})` }">

        <!-- 弹幕容器 -->
        <view v-if="gameStatus" class="danmaku-container">
            <view
                v-for="danmaku in danmakuList"
                :key="danmaku.id"
                class="danmaku-item"
                :style="{
          top: danmaku.top + 'px',
          right: danmaku.right + 'px',
          opacity: danmaku.opacity,
        }"
            >
                {{ danmaku.text }}
            </view>
        </view>

        <view class="game-tips-msg-bar">
            <game-top-msg-bar>
                <template v-slot:leftMsg>{{ countdown }}</template>
                <template v-slot:rightMsg>{{ pushUpScore }}</template>
            </game-top-msg-bar>
        </view>

        <game-tips-popup ref="gameTipsPopup" :tips-list="tipsList" :show-ad="showAD"
                         @startGame="gameTipsClose"/>

        <ready-countdown ref="readyCountdown" @countdownOver="countdownOver"/>

        <game-result-popup ref="resultPopup" :show-ad="showAD" :unit="unit"/>

        <!-- 游戏区域 -->
        <view class="game-area width100 f-d-c-j-a-c">
            <!-- 俯卧撑运动 -->
            <view class="inflator f">
                <image
                    :style="{ opacity: !pushUpsStatus ? 1 : 0 }"
                    :src="images.inflatorImg"
                    mode="widthFix"
                ></image>
                <image
                    :style="{ opacity: pushUpsStatus ? 1 : 0 }"
                    :src="images.inflator1Img"
                    mode="widthFix"
                ></image>
            </view>
        </view>

        <view v-if="gameStatus === 1" class="f-j-a-c width100 pt2">
            <view
                class="f push-button relative width50"
                :class="{ active: isButtonClicked }"
                @click.stop="pushUpsClick"
            >
                <image :src="images.pushBtnImg" mode="widthFix"/>
                <view
                    class="absolute colorfff font1_5 width100 text-center"
                    style="top: 50%; transform: translateY(-50%); letter-spacing: 2px"
                >
                    运动一下
                </view>
            </view>
        </view>
    </view>
</template>

<script>
import config from './config'

import gameTipsPopup from '../components/game-tips-popup.vue'
import gameTopMsgBar from '../components/game-top-msg-bar.vue'
import readyCountdown from '../components/ready-countdown.vue'

export default {
    components: {gameTipsPopup, gameTopMsgBar, readyCountdown},
    data() {
        return {
            images: config.images,
            defaultScore: 1, // 默认每次得分分数

            gameStatus: 0, // 0: 未开始, 1: 游戏中, 2: 已结束
            pushUpScore: 0, // 俯卧撑数量
            pushUpsStatus: false,
            isButtonClicked: false, // 按钮点击状态

            // 弹幕相关
            danmakuList: [], // 弹幕列表
            danmakuId: 0, // 弹幕ID计数器

            per_integral: 0,
            unit: '积分',
            seconds: 30,
            countdown: 30,
            score: 0,
            showAD: false
        }
    },

    computed: {
        tipsList() {
            return [
                `请在${this.seconds}秒内点击屏幕中的按钮，每次点击做一个俯卧撑。`,
                `倒计时结束后，游戏结束，每做一个俯卧撑奖励${this.per_integral}${this.unit}。`
            ]
        }
    },

    onLoad() {
        this.init()
    },

    methods: {
        async init() {
            this.$refs.gameTipsPopup.open()
        },

        gameTipsClose() {
            this.$refs.gameTipsPopup.close()
            this.$refs.readyCountdown.open()
        },

        countdownOver() {
            this.startGame()
        },

        // 开始游戏
        startGame() {
            this.gameStatus = 1
            this.countdown = this.seconds
            this.pushUpScore = 0
            this.pushUpsStatus = false
            this.danmakuList = [] // 清空弹幕
            this.startTimer()
            this.startDanmakuAnimation()
        },

        // 开始计时
        startTimer() {
            this.timer = setInterval(() => {
                this.countdown--
                if (this.countdown <= 0) this.endGame()
            }, 1000)
        },

        // 开始弹幕动画
        startDanmakuAnimation() {
            this.danmakuTimer = setInterval(() => {
                this.updateDanmaku()
            }, 16) // 约60fps
        },

        // 更新弹幕位置
        updateDanmaku() {
            this.danmakuList.forEach((danmaku) => {
                danmaku.right += 2 // 向左移动速度

                // 淡出效果
                if (danmaku.right > 300) {
                    danmaku.opacity = Math.max(0, 1 - (danmaku.right - 300) / 100)
                }
            })

            // 移除已经移出屏幕的弹幕
            this.danmakuList = this.danmakuList.filter((danmaku) => danmaku.right < 700)
        },

        // 添加弹幕
        addDanmaku(text) {
            const danmaku = {
                id: this.danmakuId++,
                text: text,
                top: Math.random() * 200 + 50, // 随机高度，限制在上方区域
                right: -100, // 从右侧开始
                opacity: 1,
            }
            this.danmakuList.push(danmaku)
        },

        // 结束游戏
        endGame() {
            this.gameStatus = 2
            if (this.timer) {
                clearInterval(this.timer)
                this.timer = null
            }
            if (this.danmakuTimer) {
                clearInterval(this.danmakuTimer)
                this.danmakuTimer = null
            }
        },

        // 运动点击
        pushUpsClick() {
            if (this.gameStatus !== 1) return

            // 触发点击动画
            this.isButtonClicked = true
            const index = Math.floor(Math.random() * config.toastList.length)

            // 添加弹幕而不是toast
            this.addDanmaku(config.toastList[index])

            setTimeout(() => (this.isButtonClicked = false), 200)

            this.pushUpsStatus = !this.pushUpsStatus
            this.pushUpScore += this.defaultScore
        }
    },

    onUnload() {
        if (this.timer) clearInterval(this.timer)
        if (this.danmakuTimer) clearInterval(this.danmakuTimer)
    },
}
</script>

<style scoped lang="scss">
.width100 {
  width: 100%;
}

.width50 {
  width: 50% !important;
}

.f {
    display: flex;
}

.f-d-c-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
}

.f-j-a-c {
    display: flex;
    justify-content: center;
    align-items: center;
}

.pt2 {
  padding-top: 2rem !important;
}

.relative {
  position: relative;
}

.absolute {
  position: absolute;
}

.colorfff {
  color: #fff !important;
}

.font1_5 {
	font-size: 1.5rem !important;
}



.content {
    width: 100%;
    min-height: 100vh;
    background-size: cover;
    background-repeat: no-repeat;
}

// 弹幕容器
.danmaku-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 300px; // 限制弹幕区域高度，不遮挡运动员
    pointer-events: none;
    z-index: 5;
    overflow: hidden;
}

// 弹幕项
.danmaku-item {
    position: absolute;
    // background: linear-gradient(45deg, #ff6b6b, #4ecdc4);
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    white-space: nowrap;
    // box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    animation: danmaku-glow 2s ease-in-out infinite alternate;
}

@keyframes danmaku-glow {
    0% {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    100% {
        box-shadow: 0 2px 15px rgba(255, 107, 107, 0.5);
    }
}

.game-area {
    padding-top: 72%;

    .inflator {
        position: relative;
        width: 80vw;
        max-width: 512px;
        height: 60vw;
        max-height: 384px;

        > image {
            position: absolute;
            left: 0;
            top: 0;
            width: 80vw;
            max-width: 512px;
            height: 60vw;
            max-height: 384px;
        }
    }
}

.push-button {
    transition: all 0.05s;

    &:active {
        transform: scale(0.9);
    }
}
</style>
