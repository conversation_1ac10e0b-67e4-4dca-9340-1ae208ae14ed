<template>
    <!-- 弹幕容器 -->
    <view class="danmaku-container">
        <view
            v-for="danmaku in danmakuList"
            :key="danmaku.id"
            class="danmaku-item"
            :style="{top: danmaku.top + 'px'}"
        >
            {{ danmaku.text }}
        </view>
    </view>
</template>

<script>
const toastList = [
    '💪 太强了！力量爆棚！',
    '🔥 标准！帅炸了！',
    '🚀 核心稳如火箭！',
    '🤯 地板都被你征服了！',
    '🌟 这动作，满分！',
    '🏆 俯卧撑王者！',
    '👏 牛！再来一个！'
]

export default {
    name: 'BiuBiuBiu',
    data() {
        return {
            // 弹幕相关数据
            danmakuList: [], // 弹幕列表
            danmakuId: 0, // 弹幕ID计数器
            danmakuDuration: 4000 // 弹幕动画持续时间（毫秒）
        }
    },

    methods: {
        // 添加弹幕 - 对外暴露的主要方法
        addDanmaku(customText = null) {
            // 如果没有传入自定义文本，则随机选择一个
            const text = customText || this.getRandomToast()

            const danmaku = {
                id: this.danmakuId++,
                text: text,
                top: Math.random() * 200 + 50, // 随机高度，限制在上方区域
            }
            this.danmakuList.push(danmaku)

            // 使用CSS动画后，需要在动画结束后自动移除弹幕
            setTimeout(() => {
                this.danmakuList = this.danmakuList.filter(item => item.id !== danmaku.id)
            }, this.danmakuDuration)
        },

        // 获取随机弹幕文本
        getRandomToast() {
            const index = Math.floor(Math.random() * toastList.length)
            return toastList[index]
        },

        // 清空所有弹幕
        clearDanmaku() {
            this.danmakuList = []
        }
    }
}
</script>

<style scoped lang="scss">
// 弹幕容器
.danmaku-container {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 300px; // 限制弹幕区域高度，不遮挡运动员
    pointer-events: none;
    z-index: 5;
    overflow: hidden;
}

// 弹幕项
.danmaku-item {
    position: absolute;
    right: -200px; // 初始位置在屏幕右侧外
    background: rgba(0, 0, 0, 0.5);
    color: #fff;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    font-weight: bold;
    white-space: nowrap;
    // 应用弹幕移动和淡出动画
    animation: danmaku-move 4s linear forwards, danmaku-glow 2s ease-in-out infinite alternate;
}

// 弹幕移动和淡出动画
@keyframes danmaku-move {
    0% {
        right: -200px; // 从右侧屏幕外开始
        opacity: 1;
    }
    75% {
        opacity: 1; // 前75%的时间保持完全不透明
    }
    100% {
        right: 100vw; // 移动到左侧屏幕外
        opacity: 0; // 最后25%的时间淡出
    }
}

// 弹幕发光效果动画
@keyframes danmaku-glow {
    0% {
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
    }
    100% {
        box-shadow: 0 2px 15px rgba(255, 107, 107, 0.5);
    }
}
</style>