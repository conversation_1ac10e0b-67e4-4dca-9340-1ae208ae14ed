/*
 * @Author: chliang<PERSON> <EMAIL>
 * @Date: 2025-07-22 14:47:46
 * @LastEditors: chliangck <EMAIL>
 * @LastEditTime: 2025-07-30 17:00:09
 * @FilePath: /线上活动王/pages/game/push-ups/config.js
 * @Description: 俯卧撑游戏配置文件
 */

const baseUrl =
  'https://7072-prod-0g479j60184f120d-1304148175.tcb.qcloud.la/public/img/answer/game/sport/'

export const config = {
  images: {
    bgImg: `${baseUrl}bg.jpg`, // 背景图片
    timeImg: `${baseUrl.replace('game/sport/', 'template/catch_doll/')}countdown.png`, // 倒计时图片
    inflatorImg: `${baseUrl}p1.jpg`, // 打气筒图片
    inflator1Img: `${baseUrl}p2.jpg`, // 打气筒压缩图片
    pushBtnImg: `${baseUrl}button.png`, // 俯卧撑按钮图片
  },
  defaultTime: 10,
  defaultScore: 10,

  toastList: [
    '💪 太强了！力量爆棚！',
    '🔥 标准！帅炸了！',
    '🚀 “核心稳如火箭！',
    '🤯 “地板都被你征服了！',
    '🌟 “这动作，满分！',
    '🏆 “俯卧撑王者！',
    '👏 牛！再来一个！',
  ],
}
