<template>
    <view class="page bg-background">

        <!-- #ifdef MP-WEIXIN -->
        <view
            v-show="add_my_small_app_show"
            class="add-my-small-app"
            @click="add_my_small_app_show = false"
        >
            <image
                class="add-my-small-app-image"
                src="https://7072-prod-9g723ognc40137fa-1253408216.tcb.qcloud.la/public/img/Smallapp/mg.png"
                mode="widthFix"
            />
        </view>
        <!-- #endif -->

        <view class="p10 text-center bg-white">

            <image
                class="avatar" mode="aspectFill"
                :src="userinfo.headimg || 'http://www.xinweiyun.com/weixin/public/avatar/1.jpg'"
            />
            <view v-if="who !== 46304">{{ userinfo.nickname || '微信用户' }}</view>
            <view class="flex-all-center font14">
                <navigator class="color-sub p5" url="./integral_record" style="display: inline-block;">
                    我的积分：{{ userinfo.integral || 0 }}
                </navigator>
                <!-- #ifdef MP-WEIXIN -->
                <button
                    class="add-integral-btn button-none color-light-primary"
                    open-type="contact"
                    @click="copy('积分', true)"
                >
                    加积分
                </button>
                <!-- #endif -->
            </view>
        </view>

        <view class="pt15"></view>

        <view class="new-fun-block bg-white">
            <view class="new-fun-block-title">我的信息</view>

            <view class="flex-row flex-wrap text-center new-fun-block-list">
                <navigator class="new-fun-block-item" url="./user_detail">
                    <text class="iconfont icon-personal-data font28 color-primary"></text>
                    <view class="font12 color-content new-fun-block-text">我的资料</view>
                </navigator>

                <!-- #ifdef MP-WEIXIN -->
                <navigator class="new-fun-block-item" url="/pages/wechat-step/wechat-step-page">
                    <text class="iconfont icon-wechat-movement font28 color-green"></text>
                    <view class="font12 color-content new-fun-block-text">微信运动步数</view>
                </navigator>

                <!-- 纯净版不显示运动海报，因为有的付费用户想要自定义里面的背景图 -->
                <navigator v-if="!independent" class="new-fun-block-item" url="/pages/wechat-step/sports-poster/sports-poster">
                    <text class="iconfont icon-image font28 color-warning"></text>
                    <view class="font12 color-content new-fun-block-text">运动海报</view>
                </navigator>
                <!-- #endif -->

                <navigator class="new-fun-block-item" url="./integral_record">
                    <text class="iconfont icon-integral font28 color-warning"></text>
                    <view class="font12 color-content new-fun-block-text">我的积分</view>
                </navigator>

                <!-- #ifdef MP-WEIXIN -->
                <navigator
                    v-if="userinfo['redpack_amount_all']"
                    class="new-fun-block-item"
                    url="/pages/wallet/withdraw"
                >
                    <uni-icons type="wallet" size="28" color="#02b148"/>
                    <view class="font12 color-content new-fun-block-text">提现</view>
                </navigator>
                <!-- #endif -->

                <navigator class="new-fun-block-item" url="/pages/activity/admin/export_record">
                    <text class="iconfont icon-export-excel font28 color-warning"></text>
                    <view class="font12 color-content new-fun-block-text">数据导出记录</view>
                </navigator>

                <!-- #ifdef MP-WEIXIN -->
                <navigator class="new-fun-block-item" url="../other/friend_list">
                    <text class="iconfont icon-team font28 color-success"></text>
                    <view class="font12 color-content new-fun-block-text">邀请好友</view>
                </navigator>
                <!-- #endif -->
            </view>
        </view>

        <view v-if="showActivityModule" class="new-fun-block bg-white">
            <view class="new-fun-block-title">活动</view>

            <view class="flex-row flex-wrap text-center new-fun-block-list">
                <view v-if="who === 288 && (evn_version === 'trial' || evn_version === 'develop') && independent" class="new-fun-block-item"
                      hover-class="navigator-hover" @click="trial2index">
                    <uni-icons type="home" size="28" color="#5cadff"/>
                    <view class="font12 color-content new-fun-block-text">返回首页</view>
                </view>

                <navigator class="new-fun-block-item" url="../activity/user/activity_list?type=all">
                    <text class="iconfont icon-dating font28 color-success"></text>
                    <view class="font12 color-content new-fun-block-text">活动广场</view>
                </navigator>

                <navigator class="new-fun-block-item" url="/pages/create-activity/index?just_template=1">
                    <uni-icons type="color" size="28" color="#5cadff"/>
                    <view class="font12 color-content new-fun-block-text">活动模版</view>
                </navigator>

                <navigator class="new-fun-block-item" url="../activity/user/activity_list?type=join">
                    <text class="iconfont icon-star font28 color-warning"></text>
                    <view class="font12 color-content new-fun-block-text">我参加的活动</view>
                </navigator>

                <navigator class="new-fun-block-item" url="../activity/user/activity_list?type=create">
                    <text class="iconfont icon-create-ticket font28 color-primary"></text>
                    <view class="font12 color-content new-fun-block-text">我创建的活动</view>
                </navigator>

                <navigator class="new-fun-block-item" url="../activity/user/activity_list?type=look_record">
                    <text class="iconfont icon-footnotes font28 color-sub"></text>
                    <view class="font12 color-content new-fun-block-text">我的足迹</view>
                </navigator>
            </view>
        </view>

        <view class="new-fun-block bg-white">
            <view class="new-fun-block-title">个人记录</view>

            <view class="flex-row flex-wrap text-center new-fun-block-list">
                <!-- #ifdef MP-WEIXIN -->
                <navigator class="new-fun-block-item" url="/pages/wechat-step/wechat-step-page">
                    <text class="iconfont icon-wechat-movement font28 color-green"></text>
                    <view class="font12 color-content new-fun-block-text">运动步数</view>
                </navigator>
                <!-- #endif -->

                <navigator class="new-fun-block-item" url="/pages/sports-center/record-list">
                    <text class="iconfont icon-badminton font28 color-primary"></text>
                    <view class="font12 color-content new-fun-block-text">运动记录</view>
                </navigator>

                <navigator class="new-fun-block-item" url="/pages/running/user/run_list">
                    <text class="iconfont icon-walk font28 color-warning"></text>
                    <view class="font12 color-content new-fun-block-text">跑步记录</view>
                </navigator>

                <navigator class="new-fun-block-item" url="/pages/weightHeight/user/details">
                    <text class="iconfont icon-electronic-scale font28 color-success"></text>
                    <view class="font12 color-content new-fun-block-text">体重记录</view>
                </navigator>

                <navigator class="new-fun-block-item" url="/pages/item-storage/item/list">
                    <text class="iconfont icon-empty-state font28" style="color: #10c8ce"></text>
                    <view class="font12 color-content new-fun-block-text">物品记录</view>
                </navigator>

                <navigator class="new-fun-block-item" url="/pages/health-management/measure-record">
                    <text class="iconfont icon-blood-sugar font28 color-error"></text>
                    <view class="font12 color-content new-fun-block-text">健康记录</view>
                </navigator>
            </view>
        </view>

        <view class="new-fun-block bg-white">
            <view class="new-fun-block-title">更多</view>

            <view class="flex-row flex-wrap text-center new-fun-block-list">
                <navigator class="new-fun-block-item" url="/pages/other/today-news">
                    <text class="iconfont icon-news font28 color-primary"></text>
                    <view class="font12 color-content new-fun-block-text">今日简讯</view>
                </navigator>

                <navigator
                    v-if="system_desc_webview_set"
                    class="new-fun-block-item" :url="'../other/webview?url=' + system_desc_webview_set.url"
                >
                    <text class="iconfont icon-copy font28 color-info"></text>
                    <view class="font12 color-content new-fun-block-text">
                        {{ system_desc_webview_set.text || '系统说明' }}
                    </view>
                </navigator>

                <!-- <navigator v-if="!independent" class="new-fun-block-item" url="../other/contact">
                  <text class="iconfont icon-chat-bubble font28 color-success"></text>
                  <view class="font12 color-content new-fun-block-text">联系客服</view>
                </navigator> -->

                <view class="new-fun-block-item" @click="clearStorage">
                    <text class="iconfont icon-delete font28 color-warning"></text>
                    <view class="font12 color-content new-fun-block-text">清除缓存</view>
                </view>

                <!--#ifndef H5-->
                <button class="new-fun-block-item new-fun-block-item-button" open-type="openSetting">
                    <text class="iconfont icon-setting font28 color-sub"></text>
                    <view class="font12 color-content new-fun-block-text">授权设置</view>
                </button>
                <!--#endif-->

                <!-- <button class="new-fun-block-item new-fun-block-item-button" open-type="feedback">
                  <i class="iconfont icon-feedback font28 color-success"/>
                  <view class="font12 color-content new-fun-block-text">意见反馈</view>
                </button> -->

                <navigator
                    v-if="evn_version === 'develop' || evn_version === 'trial'"
                    class="new-fun-block-item"
                    url="/pages/activity/other/activity_active_list"
                >
                    <text class="iconfont icon-hot font28 color-red"></text>
                    <view class="font12 color-content new-fun-block-text">平台活动活跃度排行</view>
                </navigator>

                <navigator
                    v-if="who === 288 && (evn_version === 'develop' || evn_version === 'trial')"
                    class="new-fun-block-item"
                    url="/pages/other/word-remove-markdown"
                >
                    <text class="iconfont icon-answer-sheet font28 color-sub"></text>
                    <view class="font12 color-content new-fun-block-text">去除Markdown格式</view>
                </navigator>

                <!--#ifdef H5-->
                <view class="new-fun-block-item new-fun-block-item-button" @click="logout">
                    <uni-icons type="undo" size="28" color="#ed3f14"/>
                    <view class="font12 color-content new-fun-block-text">退出登录</view>
                </view>
                <!--#endif-->
            </view>
        </view>


        <view
            class="userid color-border font12 text-center"
            @click="copy(userid, false, '会员号复制成功')"
            @longpress="setEnableDebugClick"
        >{{ template_id }}.{{ who }}.{{ userid }}
        </view>

        <tabbar v-if="show_tab && login_success"></tabbar>


        
    </view>
</template>

<script>
const app = getApp()
import login from '@/utils/api/login.js'
import xwy_api from '@/utils/api/xwy_api.js'

import tabbar from '@/components/tabbar.vue'

export default {
    components: {
        tabbar
    },
    data() {
        return {
            login_success: false,
            show_tab: false,
            userinfo: {},
            who: '',
            userid: '',
            template_id: '',
            system_desc_webview_set: null,
            independent: false,  // 是否纯净版
            evn_version: app.globalData['evn_version'],
            // #ifdef MP-WEIXIN
            add_my_small_app_show: false,
            // #endif
            showActivityModule: false
        }
    },
    watch: {
        independent() {
            this.setActivityModuleShow()
        }
    },
    
    onLoad(e) {
        this.isShowAddMySmallApp()

        if (e.from && e.from === 'tab') {
            this.show_tab = true
            this.$uni.hideHomeButton()
        }
        uni.showLoading({ mask: true })
        login.uniLogin(err => {
            uni.hideLoading()
            this.login_success = true
            if (err && err.errMsg) {
                uni.showModal({
                    title: err['errTitle'] || '提示',
                    content: err.errMsg,
                    showCancel: false
                })
            }

            this.setUserInfo()
            this.setActivityModuleShow()

            this.who = app.globalData['who']
            this.userid = app.globalData['userid']
            this.template_id = app.globalData['template_id']

            if (app.globalData['shop_data']?.shop_info?.['shop_conf_set']?.subscribe?.url) {
                this.system_desc_webview_set = app.globalData['shop_data'].shop_info['shop_conf_set'].subscribe
            }

            const shield_other_active = app.globalData['shop_info']?.extend_set?.shield_other_active
            if (shield_other_active?.active_id) {
                this.independent = true
            }


        })
    },
    onShow() {
        if (this.login_success) this.setUserInfo()
    },
    methods: {
        setActivityModuleShow() {
            // 配置了个人中心纯净版依然显示活动模块的会员号
            const userCenterActivityModuleShow = this.xwy_config.userCenterActivityModuleShow()
            if (userCenterActivityModuleShow) {
                this.showActivityModule = true
                return
            }

            // 288体验版显示活动模块
            const {who, evn_version} = this
            if (who === 288 && (evn_version === 'trial' || evn_version === 'develop')) {
                this.showActivityModule = true
                return
            }

            // 小程序后台设置了不显示活动模块
            const closed_create_active = xwy_api.closed_create_active()
            if (closed_create_active) {
                this.showActivityModule = false
                return
            }

            // 纯净版不显示活动模块
            if (this.independent) {
                this.showActivityModule = false
                return
            }


            this.showActivityModule = true
        },

        setUserInfo() {
            xwy_api.getUserDetail(() => {
                this.userinfo = app.globalData['userinfo']
            })
        },

        isShowAddMySmallApp() {
            // #ifdef MP-WEIXIN
            if (uni.getStorageSync('add_my_small_app')) return false
            this.add_my_small_app_show = true
            uni.setStorageSync('add_my_small_app', '1')
            // #endif
        },

        async trial2index() {
            this.$uni.showLoading('清除纯净版缓存...')
            const res = await this.xwy_api.request({
                url: 'front.flat.sport_step.user/clear_user_active_cache'
            })

            if (res?.status !== 1) {
                uni.hideLoading()
                return this.$uni.showModal(`纯净版缓存清除失败: ${res?.info || ''}`)
            }

            login.login_success = false
            login.uniLogin(() => {
                uni.hideLoading()
                this.$uni.reLaunch('/pages/index/index')
            })
        },


        clearStorage() {
            uni.showModal({
                title: '提示',
                content: '确定清除缓存？',
                success: res => {
                    if (res.confirm) {

                        uni.showLoading({
                            title: '清除缓存中...',
                            mask: true
                        })

                        xwy_api.ajax({
                            url: 'front.flat.sport_step.user/clear_user_active_cache',
                            data: {
                                access_token: app.globalData['access_token']
                            },
                            success: res => {
                                if (!res.status) {
                                    uni.showToast({
                                        title: '清除失败',
                                        icon: 'error'
                                    })
                                    return false
                                }


                                uni.hideLoading()
                                let useAPI = ''
                                if (wx.exitMiniProgram) useAPI = 'exit'
                                if (wx.restartMiniProgram) useAPI = 'restart'
                                if (useAPI === '') return this.$uni.showModal('缓存清除成功，请重新进入程序')
                                uni.showModal({
                                    title: '缓存清除成功',
                                    content: `缓存清除成功，需要${useAPI === 'exit' ? '重新进入' : '重启'}小程序`,
                                    cancelText: useAPI === 'exit' ? '暂不退出' : '暂不重启',
                                    confirmText: useAPI === 'exit' ? '退出程序' : '重启程序',
                                    success: res => {
                                        if (res.confirm) {
                                            if (useAPI === 'exit') return wx.exitMiniProgram({
                                                fail: err => console.log(err)
                                            })
                                            if (useAPI === 'restart') return wx.restartMiniProgram({
                                                path: '/pages/index/index'
                                            })
                                            return
                                        }

                                        uni.showLoading({
                                            mask: true
                                        })
                                        login.login_success = false;
                                        login.uniLogin(err => {
                                            uni.hideLoading()

                                            if (err && err.errMsg) {
                                                uni.showModal({
                                                    title: err['errTitle'] || '提示',
                                                    content: err.errMsg,
                                                    showCancel: false
                                                })
                                                return false
                                            }

                                            /* uni.showToast({
                                              title: '清除成功',
                                              icon: 'success'
                                            }) */

                                            let independent = false
                                            const shield_other_active = app.globalData['shop_info']?.extend_set?.shield_other_active
                                            if (shield_other_active?.active_id) {
                                                independent = true
                                            }
                                            this.independent = independent
                                        })
                                    }
                                })


                                return false

                                /* uni.clearStorage({
                                  success: res => {
                                    uni.showToast({
                                      title: '清除成功',
                                      icon: 'success'
                                    })
                                  },
                                  fail: () => {
                                    uni.hideLoading()
                                    uni.showToast({
                                      title: '清除失败',
                                      icon: 'error'
                                    })
                                  }
                                }) */


                            }
                        })


                    }
                }
            })
        },


        //统一调试事件
        setEnableDebugCommon(isDeBug = true) {
            // 关闭调试
            uni.setEnableDebug({
                enableDebug: isDeBug,
                success: () => {
                    if (isDeBug) {
                        console.log("开启调试");

                        //缓存标识已经开启调试
                        uni.setStorageSync('isDeBug', 1)

                    } else {
                        console.log("关闭调试");
                        //删除缓存标识
                        uni.removeStorageSync('isDeBug')
                    }
                }
            })
        },
        //调试点击事件
        setEnableDebugClick() {
            //查缓存是否开启了调试
            if (uni.getStorageSync('isDeBug') && uni.getStorageSync('isDeBug') === 1) {
                //关闭调试
                this.setEnableDebugCommon(false);
            } else {
                //开启调试
                this.setEnableDebugCommon(true);
            }
        },

        copyUserid() {
            uni.setClipboardData({
                data: this.userid.toString(),
                success: () => this.$uni.showToast('复制成功', 'none', 500)
            })
        },

        copy(data, hideToast = false, tips = '复制成功') {
            uni.setClipboardData({
                data: data.toString(),
                success() {
                    if (hideToast) return uni.hideToast()
                    uni.showToast({
                        title: tips,
                        icon: 'none',
                        duration: 500
                    })
                }
            })
        },

        async logout() {
            const res = await this.$uni.showModal('确定退出登录？', {showCancel: true})
            res.confirm && uni.reLaunch({
                // url: '/pages/H5/login?exit=1&back_path=' + encodeURIComponent('/pages/user/user')
                url: '/pages/H5/login?exit=1'
            })
        }
    }
}
</script>

<style scoped>
.add-my-small-app {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    z-index: 99999;
    background-color: rgba(0, 0, 0, .7);
}

.add-my-small-app-image {
    position: absolute;
    right: 0;
    top: 0;
    width: 250px;
}

.page {
    padding-bottom: 100px;
    position: relative;
    min-height: 100vh;
    box-sizing: border-box;
}

.userid {
    position: absolute;
    left: 0;
    border: 120px;
    width: 100%;
}

.avatar {
    width: 100px;
    height: 100px;
    border-radius: 50%;
}

.add-integral-btn {
    padding: 5px !important;
}

.new-fun-block {
    border-radius: 10px;
    margin: 0 10px 20px;
    box-shadow: 0 0 5px 2px #e0e0e0;
}


.new-fun-block-title {
    border-bottom: 1px solid #eee;
    padding: 10px;
}

.new-fun-block-list {
    padding: 10px 10px 5px;
}

.new-fun-block-item {
    width: 25%;
    border-radius: 10px;
    line-height: 1.5;
    padding-bottom: 10px;
}

.new-fun-block-text {
    position: relative;
    top: -3px;
}


.new-fun-block-item-button {
    background-color: #fff;
    margin: 0;
    padding: 0 0 5px;
    border: none !important;
}

.new-fun-block-item-button::after {
    content: none;
}
</style>
